/**
 * 与现有风格一致的数据分析组件
 * 使用内联样式，保持与项目整体设计的一致性
 */

'use client'

import React, { useState, useMemo } from 'react'
import { 
  BarChart3, 
  <PERSON><PERSON>hart as LineChartIcon,
  PieChart as <PERSON><PERSON>hartI<PERSON>,
  TrendingUp, 
  TrendingDown,
  Activity,
  RefreshCw,
  Download,
  Settings,
  Filter
} from 'lucide-react'
import {
  ResponsiveContainer,
  BarChart,
  LineChart,
  PieChart,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Bar,
  Line,
  Area,
  Pie,
  Cell
} from 'recharts'

interface ConsistentDataAnalyticsProps {
  experiment?: any
  onExportData?: (format: string) => void
  onRefreshData?: () => void
  className?: string
}

export default function ConsistentDataAnalytics({
  experiment,
  onExportData,
  onRefreshData,
  className
}: ConsistentDataAnalyticsProps) {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'area' | 'pie'>('bar')
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d')
  const [refreshing, setRefreshing] = useState(false)

  // 模拟数据
  const analyticsData = useMemo(() => ({
    overview: [
      { name: '成功率', value: 87.5, change: 5.2, unit: '%' },
      { name: '平均时长', value: 45.2, change: -8.3, unit: '分钟' },
      { name: '数据质量', value: 92.3, change: 0.8, unit: '分' },
      { name: '参与度', value: 68.4, change: -3.2, unit: '%' }
    ],
    timeSeriesData: [
      { name: '1月', experiments: 24, success: 21, failed: 3 },
      { name: '2月', experiments: 28, success: 25, failed: 3 },
      { name: '3月', experiments: 32, success: 29, failed: 3 },
      { name: '4月', experiments: 35, success: 31, failed: 4 },
      { name: '5月', experiments: 38, success: 34, failed: 4 },
      { name: '6月', experiments: 42, success: 38, failed: 4 }
    ],
    categoryData: [
      { name: '机器学习', value: 35, color: '#3b82f6' },
      { name: '数据处理', value: 28, color: '#8b5cf6' },
      { name: '模型优化', value: 22, color: '#ec4899' },
      { name: '特征工程', value: 15, color: '#f59e0b' }
    ]
  }), [timeRange])

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await onRefreshData?.()
      setTimeout(() => setRefreshing(false), 1500)
    } catch (error) {
      setRefreshing(false)
    }
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp style={{ height: '1rem', width: '1rem', color: '#10b981' }} />
    if (change < 0) return <TrendingDown style={{ height: '1rem', width: '1rem', color: '#ef4444' }} />
    return <Activity style={{ height: '1rem', width: '1rem', color: '#6b7280' }} />
  }

  // 自定义Tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '0.75rem',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
          padding: '1rem',
          minWidth: '200px'
        }}>
          <p style={{ fontWeight: '600', color: '#111827', marginBottom: '0.5rem' }}>{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '0.25rem' }}>
              <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>{entry.name}:</span>
              <span style={{ fontWeight: '500', color: entry.color }}>
                {entry.value}
              </span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }} className={className}>
      {/* 标题区域 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1.5rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1.5rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              padding: '1rem',
              background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              <BarChart3 style={{ height: '2rem', width: '2rem', color: 'white' }} />
            </div>
            <div>
              <h1 style={{
                fontSize: '1.875rem',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                color: 'transparent',
                margin: 0
              }}>
                数据分析中心
              </h1>
              <p style={{ color: '#6b7280', margin: '0.25rem 0 0 0' }}>深度洞察实验数据，智能分析性能趋势</p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                cursor: refreshing ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'all 0.3s ease',
                opacity: refreshing ? 0.6 : 1
              }}
              onMouseEnter={(e) => {
                if (!refreshing) {
                  e.currentTarget.style.borderColor = '#3b82f6'
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                }
              }}
              onMouseLeave={(e) => {
                if (!refreshing) {
                  e.currentTarget.style.borderColor = '#d1d5db'
                  e.currentTarget.style.boxShadow = 'none'
                }
              }}
            >
              <RefreshCw style={{ 
                height: '1rem', 
                width: '1rem',
                animation: refreshing ? 'spin 1s linear infinite' : 'none'
              }} />
              刷新数据
            </button>
            <button
              onClick={() => onExportData?.('xlsx')}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#8b5cf6'
                e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db'
                e.currentTarget.style.boxShadow = 'none'
              }}
            >
              <Download style={{ height: '1rem', width: '1rem' }} />
              导出数据
            </button>
          </div>
        </div>

        {/* 控制面板 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', flexWrap: 'wrap' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>时间范围：</span>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                padding: '0.5rem 0.75rem',
                fontSize: '0.875rem',
                cursor: 'pointer',
                transition: 'border-color 0.3s ease'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#3b82f6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            >
              <option value="7d">最近7天</option>
              <option value="30d">最近30天</option>
              <option value="90d">最近90天</option>
            </select>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>图表类型：</span>
            <select
              value={chartType}
              onChange={(e) => setChartType(e.target.value as any)}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                padding: '0.5rem 0.75rem',
                fontSize: '0.875rem',
                cursor: 'pointer',
                transition: 'border-color 0.3s ease'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#8b5cf6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            >
              <option value="bar">柱状图</option>
              <option value="line">折线图</option>
              <option value="area">面积图</option>
              <option value="pie">饼图</option>
            </select>
          </div>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem'
      }}>
        {analyticsData.overview.map((metric, index) => (
          <div
            key={metric.name}
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '1rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
              padding: '1.5rem',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
              e.currentTarget.style.transform = 'translateY(-2px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              e.currentTarget.style.transform = 'translateY(0)'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1rem' }}>
              <div style={{
                padding: '0.75rem',
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                borderRadius: '0.75rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <Activity style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
              </div>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                {getChangeIcon(metric.change)}
                <span style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: metric.change > 0 ? '#10b981' : metric.change < 0 ? '#ef4444' : '#6b7280'
                }}>
                  {Math.abs(metric.change)}%
                </span>
              </div>
            </div>
            
            <div>
              <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#6b7280', margin: '0 0 0.5rem 0' }}>
                {metric.name}
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                {metric.value}{metric.unit}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* 主图表区域 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1.5rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1.5rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            {chartType === 'bar' && <BarChart3 style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6' }} />}
            {chartType === 'line' && <LineChartIcon style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6' }} />}
            {chartType === 'area' && <Activity style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6' }} />}
            {chartType === 'pie' && <PieChartIcon style={{ height: '1.5rem', width: '1.5rem', color: '#3b82f6' }} />}
            <h2 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: '#111827',
              margin: 0
            }}>
              实验数据趋势
            </h2>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <button style={{
              background: 'white',
              border: '1px solid #d1d5db',
              borderRadius: '0.375rem',
              padding: '0.5rem',
              cursor: 'pointer',
              transition: 'all 0.3s ease'
            }}>
              <Settings style={{ height: '1rem', width: '1rem', color: '#6b7280' }} />
            </button>
          </div>
        </div>

        <div style={{ height: '400px' }}>
          <ResponsiveContainer width="100%" height="100%">
            {chartType === 'bar' && (
              <BarChart data={analyticsData.timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="experiments" fill="#3b82f6" radius={[4, 4, 0, 0]} name="总实验数" />
                <Bar dataKey="success" fill="#10b981" radius={[4, 4, 0, 0]} name="成功数" />
                <Bar dataKey="failed" fill="#ef4444" radius={[4, 4, 0, 0]} name="失败数" />
              </BarChart>
            )}

            {chartType === 'line' && (
              <LineChart data={analyticsData.timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Line type="monotone" dataKey="experiments" stroke="#3b82f6" strokeWidth={3} dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }} name="总实验数" />
                <Line type="monotone" dataKey="success" stroke="#10b981" strokeWidth={3} dot={{ fill: '#10b981', strokeWidth: 2, r: 6 }} name="成功数" />
              </LineChart>
            )}

            {chartType === 'area' && (
              <AreaChart data={analyticsData.timeSeriesData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                <YAxis stroke="#64748b" fontSize={12} />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Area type="monotone" dataKey="experiments" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} name="总实验数" />
                <Area type="monotone" dataKey="success" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} name="成功数" />
              </AreaChart>
            )}

            {chartType === 'pie' && (
              <PieChart>
                <Pie
                  data={analyticsData.categoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  fill="#8884d8"
                  dataKey="value"
                  nameKey="name"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {analyticsData.categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomTooltip />} />
                <Legend />
              </PieChart>
            )}
          </ResponsiveContainer>
        </div>
      </div>

      {/* 数据摘要 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        padding: '1.5rem'
      }}>
        <h3 style={{
          fontSize: '1.125rem',
          fontWeight: '600',
          color: '#111827',
          margin: '0 0 1rem 0'
        }}>
          数据摘要
        </h3>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem'
        }}>
          <div style={{ textAlign: 'center' }}>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0 0 0.25rem 0' }}>总实验数</p>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#3b82f6', margin: 0 }}>
              {analyticsData.timeSeriesData.reduce((sum, item) => sum + item.experiments, 0)}
            </p>
          </div>
          <div style={{ textAlign: 'center' }}>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0 0 0.25rem 0' }}>成功实验</p>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#10b981', margin: 0 }}>
              {analyticsData.timeSeriesData.reduce((sum, item) => sum + item.success, 0)}
            </p>
          </div>
          <div style={{ textAlign: 'center' }}>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0 0 0.25rem 0' }}>失败实验</p>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#ef4444', margin: 0 }}>
              {analyticsData.timeSeriesData.reduce((sum, item) => sum + item.failed, 0)}
            </p>
          </div>
          <div style={{ textAlign: 'center' }}>
            <p style={{ fontSize: '0.875rem', color: '#6b7280', margin: '0 0 0.25rem 0' }}>平均成功率</p>
            <p style={{ fontSize: '1.5rem', fontWeight: 'bold', color: '#8b5cf6', margin: 0 }}>
              {Math.round(
                (analyticsData.timeSeriesData.reduce((sum, item) => sum + item.success, 0) /
                analyticsData.timeSeriesData.reduce((sum, item) => sum + item.experiments, 0)) * 100
              )}%
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
