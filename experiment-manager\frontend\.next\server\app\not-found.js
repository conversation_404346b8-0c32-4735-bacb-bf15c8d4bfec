/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZub3QtZm91bmQmcGFnZT0lMkZub3QtZm91bmQmYXBwUGF0aHM9JnBhZ2VQYXRoPS4uJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbm90LWZvdW5kLWVycm9yLmpzJmFwcERpcj0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZtbnQlMkZlJTJGMDEtbWFpbiUyRmZyYW1ld29yayUyRmV4cGVyaW1lbnQtbWFuYWdlciUyRmZyb250ZW5kJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBLGdDQUFnQyx3T0FBdUY7QUFDdkg7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBNEc7QUFDckksb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2V4cGVyaW1lbnQtbWFuYWdlci1mcm9udGVuZC8/MGRmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcIlRVUkJPUEFDSyB7IHRyYW5zaXRpb246IG5leHQtc3NyIH1cIjtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1wYWdlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbi8vIFdlIGluamVjdCB0aGUgdHJlZSBhbmQgcGFnZXMgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IHRyZWUgPSB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICcnLFxuICAgICAgICB7XG4gICAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgICAgJ19fREVGQVVMVF9fJyxcbiAgICAgICAgICB7fSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBkZWZhdWx0UGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3BhcmFsbGVsLXJvdXRlLWRlZmF1bHRcIiksIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3BhcmFsbGVsLXJvdXRlLWRlZmF1bHRcIl0sXG4gICAgICAgICAgfVxuICAgICAgICBdXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICdsYXlvdXQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiKSwgXCIvbW50L2UvMDEtbWFpbi9mcmFtZXdvcmsvZXhwZXJpbWVudC1tYW5hZ2VyL2Zyb250ZW5kL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW107XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9ub3QtZm91bmRcIjtcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiO1xuLy8gQ3JlYXRlIGFuZCBleHBvcnQgdGhlIHJvdXRlIG1vZHVsZSB0aGF0IHdpbGwgYmUgY29uc3VtZWQuXG5leHBvcnQgY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUGFnZVJvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5BUFBfUEFHRSxcbiAgICAgICAgcGFnZTogXCIvbm90LWZvdW5kXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9ub3QtZm91bmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiBcIlwiLFxuICAgICAgICBmaWxlbmFtZTogXCJcIixcbiAgICAgICAgYXBwUGF0aHM6IFtdXG4gICAgfSxcbiAgICB1c2VybGFuZDoge1xuICAgICAgICBsb2FkZXJUcmVlOiB0cmVlXG4gICAgfVxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1wYWdlLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Ftest-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fstyles%2Fmodern-theme.css&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Ftest-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fstyles%2Fmodern-theme.css&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/test-layout.tsx */ \"(ssr)/./src/components/layout/test-layout.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJzcmMlMkZhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZhcHAlMkZnbG9iYWxzLmNzcyZtb2R1bGVzPSUyRm1udCUyRmUlMkYwMS1tYWluJTJGZnJhbWV3b3JrJTJGZXhwZXJpbWVudC1tYW5hZ2VyJTJGZnJvbnRlbmQlMkZzcmMlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGdGVzdC1sYXlvdXQudHN4Jm1vZHVsZXM9JTJGbW50JTJGZSUyRjAxLW1haW4lMkZmcmFtZXdvcmslMkZleHBlcmltZW50LW1hbmFnZXIlMkZmcm9udGVuZCUyRnNyYyUyRnN0eWxlcyUyRm1vZGVybi10aGVtZS5jc3Mmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLz8zZTNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL21udC9lLzAxLW1haW4vZnJhbWV3b3JrL2V4cGVyaW1lbnQtbWFuYWdlci9mcm9udGVuZC9zcmMvY29tcG9uZW50cy9sYXlvdXQvdGVzdC1sYXlvdXQudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp%2Fglobals.css&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fcomponents%2Flayout%2Ftest-layout.tsx&modules=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fstyles%2Fmodern-theme.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/test-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/test-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TestLayout: () => (/* binding */ TestLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/flask-conical.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,FlaskConical,Plus,Search,Settings,TrendingUp,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* __next_internal_client_entry_do_not_use__ TestLayout auto */ \n\n\n\nfunction TestLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            background: \"linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                style: {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    zIndex: 50,\n                    background: \"rgba(255, 255, 255, 0.9)\",\n                    backdropFilter: \"blur(20px)\",\n                    borderBottom: \"1px solid rgba(229, 231, 235, 0.5)\",\n                    boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.1)\",\n                    transition: \"all 0.3s ease\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"80rem\",\n                        margin: \"0 auto\",\n                        padding: \"0 1rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"space-between\",\n                            height: \"4rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    style: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        gap: \"1rem\",\n                                        textDecoration: \"none\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                position: \"relative\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"absolute\",\n                                                        inset: 0,\n                                                        background: \"linear-gradient(to right, #2563eb, #8b5cf6, #4f46e5)\",\n                                                        borderRadius: \"0.75rem\",\n                                                        filter: \"blur(4px)\",\n                                                        opacity: 0.3,\n                                                        transform: \"scale(1.1)\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        position: \"relative\",\n                                                        height: \"2.5rem\",\n                                                        width: \"2.5rem\",\n                                                        background: \"linear-gradient(to right, #2563eb, #8b5cf6, #4f46e5)\",\n                                                        borderRadius: \"0.75rem\",\n                                                        display: \"flex\",\n                                                        alignItems: \"center\",\n                                                        justifyContent: \"center\",\n                                                        boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                                                        transition: \"all 0.3s ease\"\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        style: {\n                                                            height: \"1.5rem\",\n                                                            width: \"1.5rem\",\n                                                            color: \"white\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                        lineNumber: 58,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                display: \"block\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"1.25rem\",\n                                                        fontWeight: \"bold\",\n                                                        background: \"linear-gradient(to right, #1f2937, #2563eb, #8b5cf6)\",\n                                                        WebkitBackgroundClip: \"text\",\n                                                        backgroundClip: \"text\",\n                                                        WebkitTextFillColor: \"transparent\",\n                                                        color: \"transparent\"\n                                                    },\n                                                    children: \"实验管理系统\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: \"0.75rem\",\n                                                        color: \"#6b7280\",\n                                                        marginTop: \"-0.25rem\"\n                                                    },\n                                                    children: \"Experiment Manager Pro\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        style: {\n                                            position: \"relative\",\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"white\",\n                                            background: \"linear-gradient(to right, #3b82f6, #06b6d4)\",\n                                            boxShadow: \"0 20px 25px -5px rgba(59, 130, 246, 0.3)\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.transform = \"scale(1.05)\";\n                                            e.currentTarget.style.boxShadow = \"0 25px 50px -12px rgba(59, 130, 246, 0.4)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.transform = \"scale(1)\";\n                                            e.currentTarget.style.boxShadow = \"0 20px 25px -5px rgba(59, 130, 246, 0.3)\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"仪表板\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/experiments\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"#374151\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.7)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#374151\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"实验中心\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/experiments/new\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"#374151\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.7)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#374151\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"创建实验\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/settings\",\n                                        style: {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            gap: \"0.5rem\",\n                                            padding: \"0.625rem 1.25rem\",\n                                            borderRadius: \"0.75rem\",\n                                            fontSize: \"0.875rem\",\n                                            fontWeight: \"600\",\n                                            textDecoration: \"none\",\n                                            color: \"#374151\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.7)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#374151\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"系统设置\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    display: \"flex\",\n                                    alignItems: \"center\",\n                                    gap: \"0.75rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            position: \"relative\",\n                                            display: \"flex\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    inset: 0,\n                                                    background: \"linear-gradient(to right, rgba(59, 130, 246, 0.15), rgba(139, 92, 246, 0.15))\",\n                                                    borderRadius: \"0.75rem\",\n                                                    filter: \"blur(4px)\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                style: {\n                                                    position: \"relative\",\n                                                    background: \"rgba(255, 255, 255, 0.7)\",\n                                                    backdropFilter: \"blur(12px)\",\n                                                    borderRadius: \"0.75rem\",\n                                                    border: \"1px solid rgba(229, 231, 235, 0.5)\",\n                                                    boxShadow: \"0 10px 15px -3px rgba(0, 0, 0, 0.1)\",\n                                                    transition: \"all 0.3s ease\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        style: {\n                                                            position: \"absolute\",\n                                                            left: \"1rem\",\n                                                            top: \"50%\",\n                                                            transform: \"translateY(-50%)\",\n                                                            height: \"1.25rem\",\n                                                            width: \"1.25rem\",\n                                                            color: \"#6b7280\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"search\",\n                                                        placeholder: \"搜索实验、数据、报告...\",\n                                                        style: {\n                                                            paddingLeft: \"3rem\",\n                                                            paddingRight: \"1rem\",\n                                                            width: \"18rem\",\n                                                            height: \"2.5rem\",\n                                                            background: \"transparent\",\n                                                            border: \"none\",\n                                                            outline: \"none\",\n                                                            color: \"#374151\",\n                                                            fontSize: \"0.875rem\",\n                                                            fontWeight: \"500\",\n                                                            borderRadius: \"0.75rem\"\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            position: \"relative\",\n                                            padding: \"0.5rem\",\n                                            color: \"#6b7280\",\n                                            borderRadius: \"0.75rem\",\n                                            border: \"none\",\n                                            background: \"transparent\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.5)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#6b7280\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                style: {\n                                                    height: \"1.25rem\",\n                                                    width: \"1.25rem\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                style: {\n                                                    position: \"absolute\",\n                                                    top: \"0.25rem\",\n                                                    right: \"0.25rem\",\n                                                    height: \"0.5rem\",\n                                                    width: \"0.5rem\",\n                                                    backgroundColor: \"#ef4444\",\n                                                    borderRadius: \"50%\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        style: {\n                                            position: \"relative\",\n                                            padding: \"0.5rem\",\n                                            color: \"#6b7280\",\n                                            borderRadius: \"0.75rem\",\n                                            border: \"none\",\n                                            background: \"transparent\",\n                                            cursor: \"pointer\",\n                                            transition: \"all 0.3s ease\"\n                                        },\n                                        onMouseEnter: (e)=>{\n                                            e.currentTarget.style.color = \"#111827\";\n                                            e.currentTarget.style.backgroundColor = \"rgba(255, 255, 255, 0.5)\";\n                                            e.currentTarget.style.boxShadow = \"0 4px 6px -1px rgba(0, 0, 0, 0.1)\";\n                                        },\n                                        onMouseLeave: (e)=>{\n                                            e.currentTarget.style.color = \"#6b7280\";\n                                            e.currentTarget.style.backgroundColor = \"transparent\";\n                                            e.currentTarget.style.boxShadow = \"none\";\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_FlaskConical_Plus_Search_Settings_TrendingUp_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            style: {\n                                                height: \"1.25rem\",\n                                                width: \"1.25rem\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                style: {\n                    paddingTop: \"4rem\",\n                    minHeight: \"100vh\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        maxWidth: \"80rem\",\n                        margin: \"0 auto\",\n                        padding: \"0 1rem 2rem 1rem\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            position: \"relative\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"absolute\",\n                                    inset: 0,\n                                    background: \"linear-gradient(to right, rgba(59, 130, 246, 0.05), rgba(139, 92, 246, 0.05), rgba(236, 72, 153, 0.05))\",\n                                    borderRadius: \"3rem\",\n                                    filter: \"blur(48px)\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    position: \"relative\",\n                                    background: \"rgba(255, 255, 255, 0.4)\",\n                                    backdropFilter: \"blur(10px)\",\n                                    borderRadius: \"1rem\",\n                                    border: \"1px solid rgba(255, 255, 255, 0.2)\",\n                                    boxShadow: \"0 20px 25px -5px rgba(0, 0, 0, 0.05)\",\n                                    overflow: \"hidden\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        padding: \"1.5rem 2rem\"\n                                    },\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/test-layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"347bc58f1c86\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz8xMjczIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMzQ3YmM1OGYxYzg2XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/styles/modern-theme.css":
/*!*************************************!*\
  !*** ./src/styles/modern-theme.css ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d87aeb22b8de\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL21vZGVybi10aGVtZS5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9leHBlcmltZW50LW1hbmFnZXItZnJvbnRlbmQvLi9zcmMvc3R5bGVzL21vZGVybi10aGVtZS5jc3M/YWEyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQ4N2FlYjIyYjhkZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/modern-theme.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _styles_modern_theme_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../styles/modern-theme.css */ \"(rsc)/./src/styles/modern-theme.css\");\n/* harmony import */ var _components_layout_test_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/layout/test-layout */ \"(rsc)/./src/components/layout/test-layout.tsx\");\n\n\n\n\n\nconst metadata = {\n    title: \"实验管理系统\",\n    description: '基于\"实验即契约\"理念的科研实验管理平台'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_test_layout__WEBPACK_IMPORTED_MODULE_3__.TestLayout, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/mnt/e/01-main/framework/experiment-manager/frontend/src/app/layout.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQU1NQTtBQUpnQjtBQUNhO0FBQzBCO0FBSXRELE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0Msc0VBQVVBOzBCQUNSSzs7Ozs7Ozs7Ozs7Ozs7OztBQUtYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZXhwZXJpbWVudC1tYW5hZ2VyLWZyb250ZW5kLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgJy4uL3N0eWxlcy9tb2Rlcm4tdGhlbWUuY3NzJ1xuaW1wb3J0IHsgVGVzdExheW91dCB9IGZyb20gJy4uL2NvbXBvbmVudHMvbGF5b3V0L3Rlc3QtbGF5b3V0J1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogWydsYXRpbiddIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5a6e6aqM566h55CG57O757ufJyxcbiAgZGVzY3JpcHRpb246ICfln7rkuo5cIuWunumqjOWNs+Wlkee6plwi55CG5b+155qE56eR56CU5a6e6aqM566h55CG5bmz5Y+wJyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cInpoLUNOXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxUZXN0TGF5b3V0PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9UZXN0TGF5b3V0PlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiVGVzdExheW91dCIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/layout/test-layout.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/test-layout.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TestLayout: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/mnt/e/01-main/framework/experiment-manager/frontend/src/components/layout/test-layout.tsx#TestLayout`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fmnt%2Fe%2F01-main%2Fframework%2Fexperiment-manager%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();