/**
 * 现代化数据分析组件
 * 提供全面的数据可视化和分析功能，包括多种图表类型、交互式数据探索和实时分析
 */

'use client'

import React, { useState, useMemo, useCallback } from 'react'
import { 
  BarChart3, 
  <PERSON><PERSON>hart as LineChartIcon,
  PieChart as PieChartIcon,
  TrendingUp, 
  TrendingDown,
  Activity,
  Zap,
  Target,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Settings,
  Maximize2,
  MoreHorizontal,
  Calendar,
  Clock,
  Users,
  AlertTriangle,
  CheckCircle2,
  Info,
  Sparkles
} from 'lucide-react'
import {
  ResponsiveContainer,
  Bar<PERSON>hart,
  LineChart,
  <PERSON><PERSON>hart,
  ComposedChart,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Bar,
  Line,
  Area,
  Pie,
  Cell
} from 'recharts'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Progress } from '../ui/progress'
import { Experiment } from '../../types/experiment'

interface AnalyticsData {
  name: string
  value: number
  change?: number
  trend?: number[]
  category?: string
  timestamp?: string
}

interface ModernDataAnalyticsProps {
  experiment: Experiment
  data?: AnalyticsData[]
  onExportData?: (format: string) => void
  onRefreshData?: () => void
  className?: string
}

// 现代化配色方案
const COLORS = {
  primary: ['#6366f1', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#06b6d4'],
  gradients: [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
  ]
}

export default function ModernDataAnalytics({
  experiment,
  data = [],
  onExportData,
  onRefreshData,
  className
}: ModernDataAnalyticsProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'trends' | 'comparison' | 'insights'>('overview')
  const [chartType, setChartType] = useState<'bar' | 'line' | 'area' | 'pie' | 'composed'>('bar')
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>(['all'])

  // 模拟分析数据
  const analyticsData = useMemo(() => ({
    overview: [
      { name: '成功率', value: 87.5, change: 5.2, trend: [82, 84, 85, 86, 87, 88, 87.5], unit: '%', status: 'good' },
      { name: '平均时长', value: 45.2, change: -8.3, trend: [52, 50, 48, 47, 46, 45, 45.2], unit: '分钟', status: 'good' },
      { name: '数据质量', value: 92.3, change: 0.8, trend: [90, 91, 91.5, 92, 92.2, 92.5, 92.3], unit: '分', status: 'excellent' },
      { name: '参与度', value: 68.4, change: -3.2, trend: [72, 71, 70, 69, 68.5, 68, 68.4], unit: '%', status: 'warning' }
    ],
    timeSeriesData: [
      { name: '1月', experiments: 24, success: 21, failed: 3, quality: 89 },
      { name: '2月', experiments: 28, success: 25, failed: 3, quality: 91 },
      { name: '3月', experiments: 32, success: 29, failed: 3, quality: 93 },
      { name: '4月', experiments: 35, success: 31, failed: 4, quality: 92 },
      { name: '5月', experiments: 38, success: 34, failed: 4, quality: 94 },
      { name: '6月', experiments: 42, success: 38, failed: 4, quality: 95 }
    ],
    categoryData: [
      { name: '机器学习', value: 35, color: '#6366f1' },
      { name: '数据处理', value: 28, color: '#8b5cf6' },
      { name: '模型优化', value: 22, color: '#ec4899' },
      { name: '特征工程', value: 15, color: '#f59e0b' }
    ],
    performanceMetrics: [
      { metric: 'CPU使用率', current: 65, target: 80, status: 'good' },
      { metric: '内存使用率', current: 72, target: 85, status: 'good' },
      { metric: '磁盘I/O', current: 45, target: 70, status: 'excellent' },
      { metric: '网络延迟', current: 89, target: 60, status: 'warning' }
    ]
  }), [timeRange])

  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true)
    try {
      await onRefreshData?.()
      // 模拟刷新延迟
      setTimeout(() => setIsRefreshing(false), 1500)
    } catch (error) {
      setIsRefreshing(false)
    }
  }, [onRefreshData])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-emerald-600 bg-emerald-50 border-emerald-200'
      case 'good': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'warning': return 'text-amber-600 bg-amber-50 border-amber-200'
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-emerald-600" />
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-600" />
    return <Activity className="h-4 w-4 text-gray-600" />
  }

  // 自定义Tooltip组件
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white/95 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl p-4 min-w-[200px]">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center justify-between mb-1">
              <span className="text-sm text-gray-600">{entry.name}:</span>
              <span className="font-medium" style={{ color: entry.color }}>
                {entry.value}
              </span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 现代化标题区域 */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-violet-600/10 via-purple-600/10 to-fuchsia-600/10 rounded-3xl blur-3xl" />
        <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-br from-violet-500 to-purple-600 rounded-2xl shadow-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  数据分析中心
                </h1>
                <p className="text-gray-600 mt-1">深度洞察实验数据，智能分析性能趋势</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="border-violet-200 hover:border-violet-400 hover:bg-violet-50 transition-all duration-300"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                刷新数据
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExportData?.('xlsx')}
                className="border-purple-200 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                导出数据
              </Button>
            </div>
          </div>

          {/* 控制面板 */}
          <div className="flex items-center gap-6 flex-wrap">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">时间范围：</span>
              <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
                <SelectTrigger className="w-32 bg-white/80 border-violet-200 hover:border-violet-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">最近7天</SelectItem>
                  <SelectItem value="30d">最近30天</SelectItem>
                  <SelectItem value="90d">最近90天</SelectItem>
                  <SelectItem value="1y">最近1年</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">图表类型：</span>
              <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
                <SelectTrigger className="w-32 bg-white/80 border-purple-200 hover:border-purple-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bar">柱状图</SelectItem>
                  <SelectItem value="line">折线图</SelectItem>
                  <SelectItem value="area">面积图</SelectItem>
                  <SelectItem value="pie">饼图</SelectItem>
                  <SelectItem value="composed">组合图</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* 核心指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {analyticsData.overview.map((metric, index) => (
          <Card key={metric.name} className="group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105">
            <div
              className="absolute inset-0 opacity-10"
              style={{ background: COLORS.gradients[index % COLORS.gradients.length] }}
            />
            <CardContent className="relative p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                     style={{ background: COLORS.gradients[index % COLORS.gradients.length] }}>
                  <Activity className="h-6 w-6 text-white" />
                </div>
                <Badge className={getStatusColor(metric.status)}>
                  {metric.status === 'excellent' ? '优秀' :
                   metric.status === 'good' ? '良好' :
                   metric.status === 'warning' ? '警告' : '严重'}
                </Badge>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">{metric.name}</p>
                <div className="flex items-center gap-2">
                  <p className="text-3xl font-bold text-gray-900">
                    {metric.value}{metric.unit}
                  </p>
                  <div className="flex items-center gap-1">
                    {getChangeIcon(metric.change)}
                    <span className={`text-sm font-medium ${
                      metric.change > 0 ? 'text-emerald-600' :
                      metric.change < 0 ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {Math.abs(metric.change)}%
                    </span>
                  </div>
                </div>

                {/* 迷你趋势图 */}
                <div className="h-8 flex items-end justify-between mt-3">
                  {metric.trend.map((value, idx) => (
                    <div
                      key={idx}
                      className="bg-gradient-to-t from-violet-200 to-violet-400 rounded-t transition-all duration-300 hover:from-violet-300 hover:to-violet-500"
                      style={{
                        height: `${(value / Math.max(...metric.trend)) * 100}%`,
                        width: '10%'
                      }}
                    />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 主要分析区域 */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-1">
          <TabsTrigger value="overview" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            概览分析
          </TabsTrigger>
          <TabsTrigger value="trends" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            趋势分析
          </TabsTrigger>
          <TabsTrigger value="comparison" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            对比分析
          </TabsTrigger>
          <TabsTrigger value="insights" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            智能洞察
          </TabsTrigger>
        </TabsList>

        {/* 概览分析 */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 主图表区域 */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-xl">
                      {chartType === 'bar' && <BarChart3 className="h-6 w-6 text-violet-600" />}
                      {chartType === 'line' && <LineChartIcon className="h-6 w-6 text-violet-600" />}
                      {chartType === 'pie' && <PieChartIcon className="h-6 w-6 text-violet-600" />}
                      实验数据趋势
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      {chartType === 'bar' && (
                        <BarChart data={analyticsData.timeSeriesData} accessibilityLayer>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                          <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                          <YAxis stroke="#64748b" fontSize={12} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Bar dataKey="experiments" fill="#6366f1" radius={[4, 4, 0, 0]} name="总实验数" />
                          <Bar dataKey="success" fill="#10b981" radius={[4, 4, 0, 0]} name="成功数" />
                          <Bar dataKey="failed" fill="#ef4444" radius={[4, 4, 0, 0]} name="失败数" />
                        </BarChart>
                      )}

                      {chartType === 'line' && (
                        <LineChart data={analyticsData.timeSeriesData} accessibilityLayer>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                          <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                          <YAxis stroke="#64748b" fontSize={12} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Line type="monotone" dataKey="experiments" stroke="#6366f1" strokeWidth={3} dot={{ fill: '#6366f1', strokeWidth: 2, r: 6 }} name="总实验数" />
                          <Line type="monotone" dataKey="success" stroke="#10b981" strokeWidth={3} dot={{ fill: '#10b981', strokeWidth: 2, r: 6 }} name="成功数" />
                          <Line type="monotone" dataKey="quality" stroke="#f59e0b" strokeWidth={3} dot={{ fill: '#f59e0b', strokeWidth: 2, r: 6 }} name="质量分数" />
                        </LineChart>
                      )}

                      {chartType === 'area' && (
                        <AreaChart data={analyticsData.timeSeriesData} accessibilityLayer>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                          <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                          <YAxis stroke="#64748b" fontSize={12} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Area type="monotone" dataKey="experiments" stackId="1" stroke="#6366f1" fill="#6366f1" fillOpacity={0.6} name="总实验数" />
                          <Area type="monotone" dataKey="success" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} name="成功数" />
                        </AreaChart>
                      )}

                      {chartType === 'pie' && (
                        <PieChart>
                          <Pie
                            data={analyticsData.categoryData}
                            cx="50%"
                            cy="50%"
                            outerRadius={120}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                          >
                            {analyticsData.categoryData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                        </PieChart>
                      )}

                      {chartType === 'composed' && (
                        <ComposedChart data={analyticsData.timeSeriesData} accessibilityLayer>
                          <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                          <XAxis dataKey="name" stroke="#64748b" fontSize={12} />
                          <YAxis stroke="#64748b" fontSize={12} />
                          <Tooltip content={<CustomTooltip />} />
                          <Legend />
                          <Bar dataKey="experiments" fill="#6366f1" radius={[4, 4, 0, 0]} name="总实验数" />
                          <Line type="monotone" dataKey="quality" stroke="#f59e0b" strokeWidth={3} dot={{ fill: '#f59e0b', strokeWidth: 2, r: 6 }} name="质量分数" />
                        </ComposedChart>
                      )}
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 侧边栏 - 性能指标 */}
            <div className="space-y-6">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Target className="h-5 w-5 text-emerald-600" />
                    性能指标
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {analyticsData.performanceMetrics.map((metric, index) => (
                    <div key={metric.metric} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">{metric.metric}</span>
                        <Badge className={getStatusColor(metric.status)} size="sm">
                          {metric.current}%
                        </Badge>
                      </div>
                      <div className="space-y-1">
                        <Progress
                          value={metric.current}
                          className="h-2"
                          style={{
                            background: `linear-gradient(to right, ${COLORS.primary[index % COLORS.primary.length]} 0%, ${COLORS.primary[index % COLORS.primary.length]}40 100%)`
                          }}
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>当前: {metric.current}%</span>
                          <span>目标: {metric.target}%</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* 实时状态 */}
              <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-teal-50">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Zap className="h-5 w-5 text-emerald-600" />
                    实时状态
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                      <span className="text-sm font-medium">系统运行</span>
                    </div>
                    <CheckCircle2 className="h-4 w-4 text-emerald-600" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                      <span className="text-sm font-medium">数据同步</span>
                    </div>
                    <Info className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse" />
                      <span className="text-sm font-medium">队列处理</span>
                    </div>
                    <AlertTriangle className="h-4 w-4 text-amber-600" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* 其他Tab内容占位符 */}
        <TabsContent value="trends" className="space-y-6">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-8">
            <div className="text-center">
              <TrendingUp className="h-16 w-16 text-violet-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">趋势分析</h3>
              <p className="text-gray-600">深度分析数据趋势和预测未来走向</p>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-6">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-8">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-violet-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">对比分析</h3>
              <p className="text-gray-600">多维度对比分析，发现数据差异和关联</p>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-8">
            <div className="text-center">
              <Sparkles className="h-16 w-16 text-violet-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">智能洞察</h3>
              <p className="text-gray-600">AI驱动的智能分析，自动发现数据洞察</p>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
