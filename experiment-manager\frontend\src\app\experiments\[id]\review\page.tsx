/**
 * 实验复盘页面 - 现代化内联样式版本
 * 第四阶段：实现完整的实验复盘界面
 */

'use client'

import React, { useEffect, useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  ArrowLeft,
  FileText,
  BarChart3,
  Lightbulb,
  Users,
  Download,
  Save,
  Eye
} from 'lucide-react'
import { useExperimentStore } from '../../../../store'
import { ExperimentStatus } from '../../../../types/experiment'
import { RetrospectiveType, RetrospectiveStatus } from '../../../../types/retrospective'
// 导入与现有风格一致的改进组件
import ConsistentDataAnalytics from '../../../../components/analytics/consistent-data-analytics'
import ConsistentInsightsGenerator from '../../../../components/insights/consistent-insights-generator'
// 暂时保留旧组件作为备用
import EnhancedCollaboration from '../../../../components/retrospective/enhanced-collaboration'
import EnhancedDashboard from '../../../../components/retrospective/enhanced-dashboard'
import { retrospectiveAPI } from '../../../../lib/retrospective-api'

export default function ExperimentReviewPage() {
  const params = useParams()
  const router = useRouter()
  const experimentId = params.id as string
  
  const { 
    currentExperiment, 
    loading, 
    error, 
    fetchExperimentById 
  } = useExperimentStore()

  const [activeTab, setActiveTab] = useState<'analysis' | 'insights' | 'collaboration' | 'dashboard'>('analysis')

  useEffect(() => {
    if (experimentId) {
      fetchExperimentById(experimentId)
    }
  }, [experimentId, fetchExperimentById])

  if (loading) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
          padding: '2rem'
        }}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '400px'
          }}
        >
          <div
            style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '1.5rem',
              padding: '3rem',
              textAlign: 'center',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)'
            }}
          >
            <div
              style={{
                width: '3rem',
                height: '3rem',
                border: '3px solid #e5e7eb',
                borderTop: '3px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                margin: '0 auto 1rem'
              }}
            ></div>
            <p style={{ color: '#6b7280', fontSize: '1rem', margin: 0 }}>加载实验信息中...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error || !currentExperiment) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
          padding: '2rem'
        }}
      >
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <div
            style={{
              background: 'rgba(254, 242, 242, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '1.5rem',
              padding: '3rem',
              textAlign: 'center',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(248, 113, 113, 0.2)'
            }}
          >
            <h3
              style={{
                fontSize: '1.125rem',
                fontWeight: '500',
                color: '#991b1b',
                margin: '0 0 0.5rem 0'
              }}
            >
              加载失败
            </h3>
            <p
              style={{
                color: '#dc2626',
                marginBottom: '2rem',
                fontSize: '1rem'
              }}
            >
              {error || '实验不存在'}
            </p>
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                onClick={() => router.back()}
                style={{
                  background: 'rgba(255, 255, 255, 0.8)',
                  border: '1px solid rgba(209, 213, 219, 0.8)',
                  color: '#374151',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(249, 250, 251, 0.9)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                }}
              >
                返回
              </button>
              <button
                onClick={() => fetchExperimentById(experimentId)}
                style={{
                  background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.75rem',
                  border: 'none',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                }}
              >
                重试
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // 检查实验是否已完成
  if (currentExperiment.status !== ExperimentStatus.COMPLETED) {
    return (
      <div
        style={{
          minHeight: '100vh',
          background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
          padding: '2rem'
        }}
      >
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          <div
            style={{
              background: 'rgba(254, 252, 232, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '1.5rem',
              padding: '3rem',
              textAlign: 'center',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              border: '1px solid rgba(251, 191, 36, 0.2)'
            }}
          >
            <h3
              style={{
                fontSize: '1.125rem',
                fontWeight: '500',
                color: '#92400e',
                margin: '0 0 0.5rem 0'
              }}
            >
              实验尚未完成
            </h3>
            <p
              style={{
                color: '#d97706',
                marginBottom: '2rem',
                fontSize: '1rem'
              }}
            >
              只有已完成的实验才能进行复盘分析
            </p>
            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center' }}>
              <button
                onClick={() => router.back()}
                style={{
                  background: 'rgba(255, 255, 255, 0.8)',
                  border: '1px solid rgba(209, 213, 219, 0.8)',
                  color: '#374151',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.75rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(249, 250, 251, 0.9)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                }}
              >
                返回实验详情
              </button>
              <Link href={`/experiments/${experimentId}`}>
                <button
                  style={{
                    background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                    color: 'white',
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.75rem',
                    border: 'none',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                  }}
                >
                  查看实验状态
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'analysis', label: '数据分析', icon: BarChart3, description: '实验成功率、假设准确性分析' },
    { id: 'insights', label: '洞察生成', icon: Lightbulb, description: '成功因素、失败原因、意外发现' },
    { id: 'collaboration', label: '协作讨论', icon: Users, description: '团队评论、模板管理' },
    { id: 'dashboard', label: '统计仪表板', icon: Eye, description: '复盘统计、趋势分析' }
  ] as const

  return (
    <div
      style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
        padding: '2rem'
      }}
    >
      <div style={{ maxWidth: '1400px', margin: '0 auto', display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        {/* 现代化页面头部 */}
        <header
          style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '1.5rem',
            padding: '2rem',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        >
          {/* 面包屑导航 */}
          <nav style={{ marginBottom: '1.5rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', fontSize: '0.875rem', color: '#6b7280' }}>
              <Link href="/" style={{ color: '#3b82f6', textDecoration: 'none' }}>首页</Link>
              <span>/</span>
              <Link href="/experiments" style={{ color: '#3b82f6', textDecoration: 'none' }}>实验列表</Link>
              <span>/</span>
              <Link href={`/experiments/${experimentId}`} style={{ color: '#3b82f6', textDecoration: 'none' }}>
                {currentExperiment.name}
              </Link>
              <span>/</span>
              <span style={{ color: '#1f2937' }}>复盘分析</span>
            </div>
          </nav>

          <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '2rem' }}>
            <div>
              <h1
                style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(135deg, #1f2937, #3b82f6)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: '0 0 0.5rem 0'
                }}
              >
                {currentExperiment.name} - 复盘分析
              </h1>
              <p style={{ color: '#6b7280', fontSize: '1rem', margin: 0 }}>
                实验ID: {currentExperiment.id} | 完成时间: {new Date(currentExperiment.completed_at!).toLocaleDateString('zh-CN')}
              </p>
            </div>

            <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', flexWrap: 'wrap' }}>
              <button
                onClick={() => router.back()}
                style={{
                  background: 'rgba(255, 255, 255, 0.8)',
                  border: '1px solid rgba(209, 213, 219, 0.8)',
                  color: '#374151',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(249, 250, 251, 0.9)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                }}
              >
                <ArrowLeft style={{ width: '1rem', height: '1rem' }} />
                返回实验详情
              </button>
              <button
                style={{
                  background: 'rgba(255, 255, 255, 0.8)',
                  border: '1px solid rgba(209, 213, 219, 0.8)',
                  color: '#374151',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.background = 'rgba(249, 250, 251, 0.9)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = 'rgba(255, 255, 255, 0.8)';
                }}
              >
                <Download style={{ width: '1rem', height: '1rem' }} />
                导出复盘报告
              </button>
              <button
                style={{
                  background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                  color: 'white',
                  padding: '0.5rem 1rem',
                  borderRadius: '0.5rem',
                  border: 'none',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                }}
              >
                <Save style={{ width: '1rem', height: '1rem' }} />
                保存复盘
              </button>
            </div>
          </div>
        </header>

        {/* 实验基本信息卡片 */}
        <div
          style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '1.5rem',
            padding: '2rem',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: '2rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div>
                <h3
                  style={{
                    fontSize: '1.125rem',
                    fontWeight: '500',
                    color: '#1f2937',
                    margin: '0 0 0.5rem 0'
                  }}
                >
                  {currentExperiment.name}
                </h3>
                <p style={{ color: '#6b7280', margin: 0 }}>
                  {currentExperiment.description}
                </p>
              </div>
              <div
                style={{
                  background: 'rgba(16, 185, 129, 0.1)',
                  color: '#059669',
                  padding: '0.5rem 1rem',
                  borderRadius: '1rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  border: '1px solid rgba(16, 185, 129, 0.2)'
                }}
              >
                已完成
              </div>
            </div>
            <div style={{ textAlign: 'right', fontSize: '0.875rem', color: '#6b7280' }}>
              <p style={{ margin: '0 0 0.25rem 0' }}>
                创建时间: {new Date(currentExperiment.created_at).toLocaleDateString('zh-CN')}
              </p>
              <p style={{ margin: 0 }}>
                完成时间: {new Date(currentExperiment.completed_at!).toLocaleDateString('zh-CN')}
              </p>
            </div>
          </div>
        </div>

        {/* 标签页导航 */}
        <div
          style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '1.5rem',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            overflow: 'hidden'
          }}
        >
          <div
            style={{
              borderBottom: '1px solid rgba(229, 231, 235, 0.5)',
              background: 'rgba(249, 250, 251, 0.5)'
            }}
          >
            <nav
              style={{
                display: 'flex',
                gap: '2rem',
                padding: '0 2rem'
              }}
              aria-label="复盘功能标签"
            >
              {tabs.map((tab) => {
                const Icon = tab.icon
                const isActive = activeTab === tab.id
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.5rem',
                      padding: '1rem 0.25rem',
                      borderBottom: isActive ? '2px solid #3b82f6' : '2px solid transparent',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      color: isActive ? '#2563eb' : '#6b7280',
                      background: 'none',
                      border: 'none',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      borderTop: 'none',
                      borderLeft: 'none',
                      borderRight: 'none'
                    }}
                    onMouseEnter={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.color = '#374151';
                        e.currentTarget.style.borderBottomColor = 'rgba(156, 163, 175, 0.5)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (!isActive) {
                        e.currentTarget.style.color = '#6b7280';
                        e.currentTarget.style.borderBottomColor = 'transparent';
                      }
                    }}
                  >
                    <Icon style={{ width: '1.25rem', height: '1.25rem' }} />
                    <span>{tab.label}</span>
                  </button>
                )
              })}
            </nav>
          </div>

          {/* 标签页内容 */}
          <div style={{ padding: '2rem' }}>
            {activeTab === 'analysis' && (
              <ConsistentDataAnalytics
                experiment={currentExperiment}
                onExportData={(format) => {
                  console.log(`导出分析数据: ${format}`)
                  // TODO: 实现导出功能
                }}
                onRefreshData={() => {
                  console.log('刷新分析数据')
                  // TODO: 实现刷新功能
                }}
              />
            )}

            {activeTab === 'insights' && (
              <ConsistentInsightsGenerator
                experiment={currentExperiment}
                onSaveInsight={(insight) => {
                  console.log('保存洞察:', insight)
                  // TODO: 实现保存洞察功能
                }}
                onShareInsight={(insight) => {
                  console.log('分享洞察:', insight)
                  // TODO: 实现分享洞察功能
                }}
                onApplyRecommendation={(rec) => {
                  console.log('应用建议:', rec)
                  // TODO: 实现应用建议功能
                }}
              />
            )}

            {activeTab === 'collaboration' && (
              <EnhancedCollaboration
                experiment={currentExperiment}
                onAddComment={(comment) => {
                  console.log('添加评论:', comment)
                  // TODO: 实现添加评论功能
                }}
                onInviteUser={(userId) => {
                  console.log('邀请用户:', userId)
                  // TODO: 实现邀请用户功能
                }}
              />
            )}

            {activeTab === 'dashboard' && (
              <EnhancedDashboard
                experiment={currentExperiment}
                onExportReport={(format) => {
                  console.log('导出报告:', format)
                  // TODO: 实现导出报告功能
                }}
              />
            )}
          </div>
        </div>
      </div>

      {/* 添加CSS动画 */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  )
}
