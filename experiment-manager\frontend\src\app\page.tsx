/**
 * 现代化实验管理系统首页
 * 展示系统概览和快速操作
 */

'use client'

import React from 'react'
import Link from 'next/link'
import {
  FlaskConical,
  Plus,
  TrendingUp,
  Activity,
  Clock,
  CheckCircle,
  ArrowRight,
  Zap,
  BarChart3
} from 'lucide-react'
import { Button } from '../components/ui/button'

export default function Home() {
  return (
    <div style={{ padding: '2rem 0', display: 'flex', flexDirection: 'column', gap: '2rem' }}>
      {/* 现代化欢迎横幅 */}
      <div
        style={{
          background: 'linear-gradient(135deg, #2563eb, #8b5cf6, #4f46e5)',
          color: 'white',
          padding: '3rem 2rem',
          borderRadius: '1.5rem',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          position: 'relative',
          overflow: 'hidden',
          textAlign: 'center'
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent)',
            pointerEvents: 'none'
          }}
        ></div>
        <div style={{ position: 'relative', zIndex: 10 }}>
          <h2 style={{ fontSize: '2.5rem', fontWeight: 'bold', margin: '0 0 1rem 0' }}>
            🎉 现代化实验管理系统
          </h2>
          <p style={{ fontSize: '1.25rem', opacity: 0.9, margin: 0 }}>
            全新的现代化界面已激活，享受更优雅的科研管理体验
          </p>
        </div>
      </div>

      {/* 欢迎区域 */}
      <div style={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        <div style={{ position: 'relative', display: 'inline-block', alignSelf: 'center' }}>
          <div
            style={{
              position: 'absolute',
              inset: 0,
              background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
              borderRadius: '1rem',
              filter: 'blur(8px)',
              opacity: 0.2
            }}
          ></div>
          <h1
            style={{
              position: 'relative',
              fontSize: 'clamp(2.5rem, 5vw, 4rem)',
              fontWeight: 'bold',
              background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              color: 'transparent',
              margin: 0
            }}
          >
            实验管理系统
          </h1>
        </div>
        <p style={{
          fontSize: '1.25rem',
          color: '#6b7280',
          maxWidth: '48rem',
          margin: '0 auto',
          lineHeight: '1.6'
        }}>
          基于"实验即契约"理念的现代化科研管理平台，让每个实验都可追溯、可复现
        </p>

        {/* 快速操作按钮 */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '1rem',
          paddingTop: '1rem'
        }}>
          <Link href="/experiments/new">
            <button
              style={{
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                color: 'white',
                padding: '0.75rem 2rem',
                fontSize: '1.125rem',
                fontWeight: '600',
                borderRadius: '0.75rem',
                border: 'none',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #1d4ed8, #7c3aed)';
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #8b5cf6)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <Plus className="h-5 w-5" />
              创建新实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>

          <Link href="/experiments">
            <button
              style={{
                background: 'white',
                color: '#374151',
                padding: '0.75rem 2rem',
                fontSize: '1.125rem',
                fontWeight: '600',
                borderRadius: '0.75rem',
                border: '2px solid #d1d5db',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#3b82f6';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db';
                e.currentTarget.style.boxShadow = 'none';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <FlaskConical className="h-5 w-5" />
              查看所有实验
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>

          {/* 新增：一致性UI改进展示链接 */}
          <Link href="/consistent-ui-demo">
            <button
              style={{
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                color: 'white',
                padding: '0.75rem 2rem',
                fontSize: '1.125rem',
                fontWeight: '600',
                borderRadius: '0.75rem',
                border: 'none',
                boxShadow: '0 10px 15px -3px rgba(59, 130, 246, 0.3)',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #2563eb, #7c3aed)';
                e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(59, 130, 246, 0.4)';
                e.currentTarget.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'linear-gradient(to right, #3b82f6, #8b5cf6)';
                e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(59, 130, 246, 0.3)';
                e.currentTarget.style.transform = 'translateY(0)';
              }}
            >
              <BarChart3 className="h-5 w-5" />
              ✨ 查看UI改进
              <ArrowRight className="h-5 w-5" />
            </button>
          </Link>
        </div>
      </div>

      {/* 快速统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
        gap: '1.5rem'
      }}>
        {/* 总实验数 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #eff6ff, #dbeafe)',
            borderRadius: '1rem',
            padding: '1.5rem',
            border: '1px solid #93c5fd',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(-4px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#2563eb', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                总实验数
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1e3a8a', margin: 0 }}>
                24
              </p>
            </div>
            <FlaskConical style={{ height: '2rem', width: '2rem', color: '#3b82f6' }} />
          </div>
        </div>

        {/* 已完成 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #f0fdf4, #dcfce7)',
            borderRadius: '1rem',
            padding: '1.5rem',
            border: '1px solid #86efac',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(-4px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#059669', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                已完成
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#064e3b', margin: 0 }}>
                18
              </p>
            </div>
            <CheckCircle style={{ height: '2rem', width: '2rem', color: '#10b981' }} />
          </div>
        </div>

        {/* 进行中 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #fffbeb, #fed7aa)',
            borderRadius: '1rem',
            padding: '1.5rem',
            border: '1px solid #fdba74',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(-4px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#d97706', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                进行中
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#92400e', margin: 0 }}>
                4
              </p>
            </div>
            <Activity style={{ height: '2rem', width: '2rem', color: '#f59e0b' }} />
          </div>
        </div>

        {/* 成功率 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #faf5ff, #e9d5ff)',
            borderRadius: '1rem',
            padding: '1.5rem',
            border: '1px solid #c4b5fd',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            cursor: 'pointer'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(-4px)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
            e.currentTarget.style.transform = 'translateY(0)';
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ color: '#7c3aed', fontSize: '0.875rem', fontWeight: '500', margin: '0 0 0.5rem 0' }}>
                成功率
              </p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#581c87', margin: 0 }}>
                92%
              </p>
            </div>
            <TrendingUp style={{ height: '2rem', width: '2rem', color: '#8b5cf6' }} />
          </div>
        </div>
      </div>

      {/* 功能特色区域 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(400px, 1fr))',
        gap: '2rem'
      }}>
        {/* 核心功能 */}
        <div
          style={{
            background: 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(10px)',
            borderRadius: '1.5rem',
            padding: '2rem',
            border: '1px solid rgba(229, 231, 235, 0.5)',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
          }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div
                style={{
                  padding: '1rem',
                  background: 'linear-gradient(135deg, #3b82f6, #8b5cf6)',
                  borderRadius: '1rem',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
              >
                <Zap style={{ height: '2rem', width: '2rem', color: 'white' }} />
              </div>
              <h2
                style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  color: 'transparent',
                  margin: 0
                }}
              >
                核心功能
              </h2>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.25rem' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '1rem',
                  padding: '1rem',
                  background: 'rgba(239, 246, 255, 0.5)',
                  borderRadius: '0.75rem',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 0.8)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 0.5)';
                }}
              >
                <div
                  style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    background: 'linear-gradient(to right, #3b82f6, #2563eb)',
                    marginTop: '6px',
                    flexShrink: 0
                  }}
                ></div>
                <div>
                  <h3 style={{ fontWeight: '600', color: '#111827', margin: '0 0 0.25rem 0' }}>
                    实验全生命周期管理
                  </h3>
                  <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                    从假设到结论的完整追踪，确保每个实验都可追溯
                  </p>
                </div>
              </div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '1rem',
                  padding: '1rem',
                  background: 'rgba(250, 245, 255, 0.5)',
                  borderRadius: '0.75rem',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(250, 245, 255, 0.8)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(250, 245, 255, 0.5)';
                }}
              >
                <div
                  style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    background: 'linear-gradient(to right, #8b5cf6, #7c3aed)',
                    marginTop: '6px',
                    flexShrink: 0
                  }}
                ></div>
                <div>
                  <h3 style={{ fontWeight: '600', color: '#111827', margin: '0 0 0.25rem 0' }}>
                    智能复盘系统
                  </h3>
                  <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                    三阶段十二步科学复盘流程，深度挖掘实验价值
                  </p>
                </div>
              </div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'flex-start',
                  gap: '1rem',
                  padding: '1rem',
                  background: 'rgba(240, 253, 244, 0.5)',
                  borderRadius: '0.75rem',
                  transition: 'background-color 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(240, 253, 244, 0.8)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(240, 253, 244, 0.5)';
                }}
              >
                <div
                  style={{
                    width: '12px',
                    height: '12px',
                    borderRadius: '50%',
                    background: 'linear-gradient(to right, #10b981, #059669)',
                    marginTop: '6px',
                    flexShrink: 0
                  }}
                ></div>
                <div>
                  <h3 style={{ fontWeight: '600', color: '#111827', margin: '0 0 0.25rem 0' }}>
                    数据可视化分析
                  </h3>
                  <p style={{ color: '#6b7280', fontSize: '0.875rem', margin: 0 }}>
                    直观的图表和趋势分析，让数据说话
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 最近活动 */}
        <div
          style={{
            background: 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(10px)',
            borderRadius: '1.5rem',
            padding: '2rem',
            border: '1px solid rgba(229, 231, 235, 0.5)',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.25)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)';
          }}
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
              <div
                style={{
                  padding: '1rem',
                  background: 'linear-gradient(135deg, #8b5cf6, #ec4899)',
                  borderRadius: '1rem',
                  boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                }}
              >
                <BarChart3 style={{ height: '2rem', width: '2rem', color: 'white' }} />
              </div>
              <h2
                style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  background: 'linear-gradient(to right, #8b5cf6, #ec4899)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  color: 'transparent',
                  margin: 0
                }}
              >
                最近活动
              </h2>
            </div>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '1.25rem',
                  background: 'rgba(240, 253, 244, 0.8)',
                  borderRadius: '0.75rem',
                  border: '1px solid rgba(134, 239, 172, 0.5)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(240, 253, 244, 1)';
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(240, 253, 244, 0.8)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <div style={{ padding: '0.5rem', background: '#10b981', borderRadius: '0.5rem' }}>
                  <CheckCircle style={{ height: '1.25rem', width: '1.25rem', color: 'white' }} />
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{ fontWeight: '600', color: '#111827', margin: '0 0 0.25rem 0' }}>
                    深度学习模型训练
                  </p>
                  <p style={{ fontSize: '0.875rem', color: '#059669', fontWeight: '500', margin: 0 }}>
                    2小时前完成
                  </p>
                </div>
              </div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '1.25rem',
                  background: 'rgba(239, 246, 255, 0.8)',
                  borderRadius: '0.75rem',
                  border: '1px solid rgba(147, 197, 253, 0.5)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 1)';
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(239, 246, 255, 0.8)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <div style={{ padding: '0.5rem', background: '#3b82f6', borderRadius: '0.5rem' }}>
                  <Activity style={{ height: '1.25rem', width: '1.25rem', color: 'white' }} />
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{ fontWeight: '600', color: '#111827', margin: '0 0 0.25rem 0' }}>
                    数据预处理实验
                  </p>
                  <p style={{ fontSize: '0.875rem', color: '#2563eb', fontWeight: '500', margin: 0 }}>
                    正在进行中
                  </p>
                </div>
              </div>

              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '1rem',
                  padding: '1.25rem',
                  background: 'rgba(255, 251, 235, 0.8)',
                  borderRadius: '0.75rem',
                  border: '1px solid rgba(253, 186, 116, 0.5)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 251, 235, 1)';
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 251, 235, 0.8)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                <div style={{ padding: '0.5rem', background: '#f59e0b', borderRadius: '0.5rem' }}>
                  <Clock style={{ height: '1.25rem', width: '1.25rem', color: 'white' }} />
                </div>
                <div style={{ flex: 1 }}>
                  <p style={{ fontWeight: '600', color: '#111827', margin: '0 0 0.25rem 0' }}>
                    模型评估分析
                  </p>
                  <p style={{ fontSize: '0.875rem', color: '#d97706', fontWeight: '500', margin: 0 }}>
                    等待开始
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
