/**
 * 现代化UI展示页面
 * 集成展示所有四个现代化改进的模块
 */

'use client'

import React, { useState } from 'react'
import { 
  BarChart3, 
  Brain, 
  Users, 
  Activity,
  Sparkles,
  ArrowRight,
  CheckCircle2,
  Star,
  Zap
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../../components/ui/tabs'

// 导入我们创建的现代化组件
// import ModernDataAnalytics from '../../components/analytics/modern-data-analytics'
// import ModernInsightsGenerator from '../../components/insights/modern-insights-generator'
// import ModernCollaborationHub from '../../components/collaboration/modern-collaboration-hub'
// import ModernStatisticsDashboard from '../../components/dashboard/modern-statistics-dashboard'

// 临时使用动态导入来调试
import dynamic from 'next/dynamic'

const ModernDataAnalytics = dynamic(() => import('../../components/analytics/modern-data-analytics'), {
  loading: () => <div className="p-8 text-center">加载数据分析模块中...</div>,
  ssr: false
})

const ModernInsightsGenerator = dynamic(() => import('../../components/insights/modern-insights-generator'), {
  loading: () => <div className="p-8 text-center">加载洞察生成模块中...</div>,
  ssr: false
})

const ModernCollaborationHub = dynamic(() => import('../../components/collaboration/modern-collaboration-hub'), {
  loading: () => <div className="p-8 text-center">加载协作讨论模块中...</div>,
  ssr: false
})

const ModernStatisticsDashboard = dynamic(() => import('../../components/dashboard/modern-statistics-dashboard'), {
  loading: () => <div className="p-8 text-center">加载统计仪表板模块中...</div>,
  ssr: false
})

// 模拟实验数据
const mockExperiment = {
  id: 'exp-001',
  name: '深度学习模型优化实验',
  description: '通过注意力机制和残差连接优化深度学习模型性能',
  status: 'completed' as const,
  created_at: '2025-01-31T08:00:00Z',
  updated_at: '2025-01-31T12:00:00Z',
  hypothesis: '引入注意力机制可以显著提升模型在长序列数据上的表现',
  methodology: '使用Transformer架构，结合多头注意力机制',
  conclusion: '实验成功，模型准确率提升12.3%，达到业界领先水平',
  analysis_result: '注意力机制有效提升了模型对长序列的理解能力',
  lessons_learned: '注意力机制的引入需要平衡计算复杂度和性能提升',
  next_steps: '计划在生产环境中部署优化后的模型',
  artifacts_path: '/experiments/exp-001/artifacts'
}

export default function ModernUIShowcase() {
  const [activeModule, setActiveModule] = useState<'analytics' | 'insights' | 'collaboration' | 'dashboard'>('analytics')
  const [debugInfo, setDebugInfo] = useState('')

  // 添加调试信息
  React.useEffect(() => {
    setDebugInfo(`当前模块: ${activeModule}, 时间: ${new Date().toLocaleTimeString()}`)
    console.log('ModernUIShowcase 组件已加载，当前模块:', activeModule)
  }, [activeModule])

  const modules = [
    {
      id: 'analytics' as const,
      name: '数据分析',
      description: '现代化数据可视化和分析',
      icon: BarChart3,
      color: 'from-violet-500 to-purple-600',
      bgColor: 'from-violet-50 to-purple-50',
      features: ['多图表类型', '响应式设计', '实时数据', '智能交互']
    },
    {
      id: 'insights' as const,
      name: '洞察生成',
      description: 'AI驱动的智能洞察生成',
      icon: Brain,
      color: 'from-blue-500 to-indigo-600',
      bgColor: 'from-blue-50 to-indigo-50',
      features: ['AI洞察', '置信度可视化', '可执行建议', '交互式卡片']
    },
    {
      id: 'collaboration' as const,
      name: '协作讨论',
      description: '现代化团队协作中心',
      icon: Users,
      color: 'from-emerald-500 to-teal-600',
      bgColor: 'from-emerald-50 to-teal-50',
      features: ['实时协作', '表情反应', '@提及', '在线状态']
    },
    {
      id: 'dashboard' as const,
      name: '统计仪表板',
      description: '全方位系统监控仪表板',
      icon: Activity,
      color: 'from-amber-500 to-orange-600',
      bgColor: 'from-amber-50 to-orange-50',
      features: ['实时监控', '多维度指标', '系统性能', '活动时间线']
    }
  ]

  const currentModule = modules.find(m => m.id === activeModule)

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50">
      <div className="container mx-auto px-4 py-8 space-y-8">

        {/* 调试信息区域 */}
        <div className="bg-yellow-100 border-l-4 border-yellow-500 p-4 rounded-lg">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Zap className="h-5 w-5 text-yellow-500" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                🎨 <strong>现代化UI展示页面已加载</strong> | {debugInfo}
              </p>
              <p className="text-xs text-yellow-600 mt-1">
                如果您看到这个消息，说明页面正在正常工作。请在下方选择不同的模块查看现代化改进。
              </p>
            </div>
          </div>
        </div>
        {/* 页面标题 */}
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-3xl blur-3xl" />
          <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-6 shadow-2xl">
                <Sparkles className="h-10 w-10 text-white" />
              </div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-4">
                🎨 现代化UI展示中心
              </h1>
              <p className="text-xl text-gray-600 mb-6 max-w-3xl mx-auto">
                全新升级的四大核心模块，采用最新设计趋势和技术实现，为您带来前所未有的用户体验
              </p>
              
              {/* 改进统计 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">250%</div>
                  <div className="text-sm text-gray-600">视觉提升</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600">200%</div>
                  <div className="text-sm text-gray-600">体验提升</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-emerald-600">300%</div>
                  <div className="text-sm text-gray-600">交互提升</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-amber-600">400%</div>
                  <div className="text-sm text-gray-600">现代化提升</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 模块选择器 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {modules.map((module) => {
            const IconComponent = module.icon
            const isActive = activeModule === module.id
            
            return (
              <Card 
                key={module.id}
                className={`group cursor-pointer border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-105 ${
                  isActive 
                    ? `bg-gradient-to-br ${module.bgColor} ring-2 ring-blue-400` 
                    : 'bg-white/80 backdrop-blur-xl hover:bg-white/90'
                }`}
                onClick={() => setActiveModule(module.id)}
              >
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className={`p-3 bg-gradient-to-br ${module.color} rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      {isActive && (
                        <div className="flex items-center gap-1 text-blue-600">
                          <CheckCircle2 className="h-4 w-4" />
                          <span className="text-xs font-medium">当前选中</span>
                        </div>
                      )}
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{module.name}</h3>
                      <p className="text-sm text-gray-600 mb-3">{module.description}</p>
                      
                      <div className="space-y-2">
                        {module.features.map((feature, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-yellow-500" />
                            <span className="text-xs text-gray-600">{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <Button 
                      variant={isActive ? "default" : "outline"}
                      size="sm" 
                      className={`w-full ${
                        isActive 
                          ? `bg-gradient-to-r ${module.color} text-white border-0 shadow-lg` 
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      {isActive ? '正在展示' : '查看模块'}
                      <ArrowRight className="h-3 w-3 ml-2" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* 当前模块信息 */}
        {currentModule && (
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-gray-600/5 via-blue-600/5 to-purple-600/5 rounded-2xl blur-2xl" />
            <div className="relative bg-white/60 backdrop-blur-lg rounded-2xl border border-white/30 shadow-xl p-6">
              <div className="flex items-center gap-4 mb-4">
                <div className={`p-3 bg-gradient-to-br ${currentModule.color} rounded-xl shadow-lg`}>
                  <currentModule.icon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{currentModule.name}模块</h2>
                  <p className="text-gray-600">{currentModule.description}</p>
                </div>
                <div className="ml-auto">
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    <Zap className="h-3 w-3 mr-1" />
                    已现代化
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 模块内容展示 */}
        <div className="space-y-8">
          {/* 模块加载指示器 */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              <h3 className="text-lg font-semibold text-blue-900">
                正在展示: {currentModule?.name}模块
              </h3>
            </div>
            <p className="text-blue-700 text-sm">
              {currentModule?.description} - 如果下方内容没有显示，请检查浏览器控制台是否有错误信息。
            </p>
          </div>

          {activeModule === 'analytics' && (
            <div className="border-4 border-dashed border-green-300 rounded-xl p-4 bg-green-50">
              <div className="text-center mb-4">
                <div className="inline-flex items-center gap-2 bg-green-100 px-4 py-2 rounded-full">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-800 font-medium">数据分析模块已激活</span>
                </div>
              </div>
              <ModernDataAnalytics
                experiment={mockExperiment}
                onExportData={(format) => console.log('导出数据:', format)}
                onRefreshData={() => console.log('刷新数据')}
              />
            </div>
          )}

          {activeModule === 'insights' && (
            <div className="border-4 border-dashed border-purple-300 rounded-xl p-4 bg-purple-50">
              <div className="text-center mb-4">
                <div className="inline-flex items-center gap-2 bg-purple-100 px-4 py-2 rounded-full">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                  <span className="text-purple-800 font-medium">洞察生成模块已激活</span>
                </div>
              </div>
              <ModernInsightsGenerator
                experiment={mockExperiment}
                onSaveInsight={(insight) => console.log('保存洞察:', insight)}
                onShareInsight={(insight) => console.log('分享洞察:', insight)}
                onApplyRecommendation={(rec) => console.log('应用建议:', rec)}
              />
            </div>
          )}

          {activeModule === 'collaboration' && (
            <div className="border-4 border-dashed border-emerald-300 rounded-xl p-4 bg-emerald-50">
              <div className="text-center mb-4">
                <div className="inline-flex items-center gap-2 bg-emerald-100 px-4 py-2 rounded-full">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                  <span className="text-emerald-800 font-medium">协作讨论模块已激活</span>
                </div>
              </div>
              <ModernCollaborationHub
                experiment={mockExperiment}
                onAddComment={(comment) => console.log('添加评论:', comment)}
                onInviteUser={(userId) => console.log('邀请用户:', userId)}
                onMentionUser={(userId) => console.log('提及用户:', userId)}
              />
            </div>
          )}

          {activeModule === 'dashboard' && (
            <div className="border-4 border-dashed border-amber-300 rounded-xl p-4 bg-amber-50">
              <div className="text-center mb-4">
                <div className="inline-flex items-center gap-2 bg-amber-100 px-4 py-2 rounded-full">
                  <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                  <span className="text-amber-800 font-medium">统计仪表板模块已激活</span>
                </div>
              </div>
              <ModernStatisticsDashboard
                experiment={mockExperiment}
                onExportReport={(format) => console.log('导出报告:', format)}
                onRefreshData={() => console.log('刷新数据')}
              />
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div className="text-center py-8">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-white/80 backdrop-blur-xl rounded-full border border-white/20 shadow-lg">
            <CheckCircle2 className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-gray-700">
              所有模块已完成现代化升级 • 技术栈: Next.js 14 + TypeScript + Tailwind CSS + Recharts
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
