/**
 * 现代化协作讨论中心
 * 提供实时协作、智能评论系统和团队互动功能
 */

'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { 
  Users, 
  MessageSquare, 
  Send, 
  Reply, 
  Heart, 
  Share2, 
  MoreHorizontal,
  Pin,
  Flag,
  Edit3,
  Trash2,
  UserPlus,
  Bell,
  Filter,
  Search,
  Tag,
  Calendar,
  Clock,
  CheckCircle2,
  AlertCircle,
  Info,
  Lightbulb,
  Smile,
  Paperclip,
  Image,
  Mic,
  Video,
  AtSign,
  Hash,
  Zap,
  Star,
  Eye,
  MessageCircle,
  ThumbsUp,
  Bookmark,
  Settings,
  Plus
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { Textarea } from '../ui/textarea'
import { Input } from '../ui/input'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Experiment } from '../../types/experiment'

interface TeamMember {
  id: string
  name: string
  avatar?: string
  role: string
  department: string
  status: 'online' | 'away' | 'offline'
  lastSeen?: string
}

interface Comment {
  id: string
  author: TeamMember
  content: string
  type: 'comment' | 'suggestion' | 'question' | 'insight' | 'announcement'
  timestamp: string
  likes: number
  replies: Comment[]
  isPinned: boolean
  isResolved?: boolean
  tags: string[]
  liked?: boolean
  bookmarked?: boolean
  mentions: string[]
  attachments?: {
    type: 'image' | 'file' | 'link'
    url: string
    name: string
  }[]
  reactions: {
    emoji: string
    count: number
    users: string[]
  }[]
}

interface ModernCollaborationHubProps {
  experiment: Experiment
  onAddComment?: (comment: any) => void
  onInviteUser?: (userId: string) => void
  onMentionUser?: (userId: string) => void
  className?: string
}

// 评论类型配置
const COMMENT_TYPES = {
  comment: {
    label: '评论',
    icon: MessageSquare,
    color: 'text-blue-600',
    bg: 'bg-blue-50',
    border: 'border-blue-200'
  },
  suggestion: {
    label: '建议',
    icon: Lightbulb,
    color: 'text-yellow-600',
    bg: 'bg-yellow-50',
    border: 'border-yellow-200'
  },
  question: {
    label: '问题',
    icon: AlertCircle,
    color: 'text-orange-600',
    bg: 'bg-orange-50',
    border: 'border-orange-200'
  },
  insight: {
    label: '洞察',
    icon: Zap,
    color: 'text-purple-600',
    bg: 'bg-purple-50',
    border: 'border-purple-200'
  },
  announcement: {
    label: '公告',
    icon: Info,
    color: 'text-green-600',
    bg: 'bg-green-50',
    border: 'border-green-200'
  }
}

export default function ModernCollaborationHub({
  experiment,
  onAddComment,
  onInviteUser,
  onMentionUser,
  className
}: ModernCollaborationHubProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState('')
  const [commentType, setCommentType] = useState<keyof typeof COMMENT_TYPES>('comment')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [replyContent, setReplyContent] = useState('')
  const [filterType, setFilterType] = useState<'all' | keyof typeof COMMENT_TYPES>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)
  const [activeTab, setActiveTab] = useState<'discussions' | 'team' | 'activity'>('discussions')
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // 模拟团队成员数据
  const teamMembers: TeamMember[] = [
    {
      id: 'user1',
      name: '张研究员',
      avatar: '/avatars/zhang.jpg',
      role: '高级研究员',
      department: 'AI研究部',
      status: 'online'
    },
    {
      id: 'user2',
      name: '李工程师',
      avatar: '/avatars/li.jpg',
      role: '机器学习工程师',
      department: '技术部',
      status: 'online'
    },
    {
      id: 'user3',
      name: '王博士',
      avatar: '/avatars/wang.jpg',
      role: '数据科学家',
      department: '研发中心',
      status: 'away'
    },
    {
      id: 'user4',
      name: '陈主管',
      avatar: '/avatars/chen.jpg',
      role: '项目主管',
      department: '管理部',
      status: 'offline',
      lastSeen: '2小时前'
    }
  ]

  // 模拟评论数据
  const mockComments: Comment[] = [
    {
      id: '1',
      author: teamMembers[0],
      content: '这个实验的结果非常令人印象深刻！🎉 特别是在处理长序列数据时的表现。我建议我们可以进一步探索注意力机制的优化，可能会带来更好的效果。@李工程师 你觉得呢？',
      type: 'suggestion',
      timestamp: '2025-01-31T09:30:00Z',
      likes: 8,
      replies: [],
      isPinned: true,
      tags: ['性能优化', '注意力机制'],
      liked: true,
      bookmarked: true,
      mentions: ['user2'],
      reactions: [
        { emoji: '👍', count: 5, users: ['user2', 'user3', 'user4', 'user5', 'user6'] },
        { emoji: '🚀', count: 3, users: ['user1', 'user3', 'user4'] },
        { emoji: '💡', count: 2, users: ['user2', 'user5'] }
      ]
    },
    {
      id: '2',
      author: teamMembers[1],
      content: '同意张研究员的观点！我已经在本地测试了几种注意力机制的变体，初步结果显示多头注意力确实能带来显著提升。我会整理一份详细的技术报告分享给大家。',
      type: 'comment',
      timestamp: '2025-01-31T10:15:00Z',
      likes: 6,
      replies: [],
      isPinned: false,
      tags: ['技术分析', '多头注意力'],
      liked: false,
      bookmarked: false,
      mentions: [],
      reactions: [
        { emoji: '👏', count: 4, users: ['user1', 'user3', 'user4', 'user5'] },
        { emoji: '📊', count: 2, users: ['user1', 'user3'] }
      ]
    },
    {
      id: '3',
      author: teamMembers[2],
      content: '从数据科学的角度来看，我们还需要考虑数据分布的影响。建议增加更多样化的测试数据集，确保模型的泛化能力。另外，我注意到在某些边缘情况下模型表现不够稳定，这个问题需要重点关注。',
      type: 'insight',
      timestamp: '2025-01-31T11:00:00Z',
      likes: 4,
      replies: [],
      isPinned: false,
      tags: ['数据分析', '泛化能力', '边缘情况'],
      liked: true,
      bookmarked: false,
      mentions: [],
      reactions: [
        { emoji: '🤔', count: 3, users: ['user1', 'user2', 'user4'] },
        { emoji: '📈', count: 2, users: ['user1', 'user2'] }
      ]
    }
  ]

  useEffect(() => {
    // 模拟加载评论
    setTimeout(() => {
      setComments(mockComments)
    }, 500)
  }, [])

  const handleSubmitComment = useCallback(() => {
    if (!newComment.trim()) return

    const comment: Comment = {
      id: Date.now().toString(),
      author: teamMembers[0], // 当前用户
      content: newComment,
      type: commentType,
      timestamp: new Date().toISOString(),
      likes: 0,
      replies: [],
      isPinned: false,
      tags: [],
      liked: false,
      bookmarked: false,
      mentions: [],
      reactions: []
    }

    setComments(prev => [comment, ...prev])
    setNewComment('')
    onAddComment?.(comment)
  }, [newComment, commentType, onAddComment])

  const handleSubmitReply = useCallback((parentId: string) => {
    if (!replyContent.trim()) return

    const reply: Comment = {
      id: `${parentId}-${Date.now()}`,
      author: teamMembers[0],
      content: replyContent,
      type: 'comment',
      timestamp: new Date().toISOString(),
      likes: 0,
      replies: [],
      isPinned: false,
      tags: [],
      liked: false,
      bookmarked: false,
      mentions: [],
      reactions: []
    }

    setComments(prev => prev.map(comment => 
      comment.id === parentId 
        ? { ...comment, replies: [...comment.replies, reply] }
        : comment
    ))
    
    setReplyContent('')
    setReplyingTo(null)
  }, [replyContent])

  const toggleLike = useCallback((commentId: string) => {
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? { 
            ...comment, 
            liked: !comment.liked,
            likes: comment.liked ? comment.likes - 1 : comment.likes + 1
          }
        : comment
    ))
  }, [])

  const toggleBookmark = useCallback((commentId: string) => {
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? { ...comment, bookmarked: !comment.bookmarked }
        : comment
    ))
  }, [])

  const addReaction = useCallback((commentId: string, emoji: string) => {
    setComments(prev => prev.map(comment => {
      if (comment.id === commentId) {
        const existingReaction = comment.reactions.find(r => r.emoji === emoji)
        if (existingReaction) {
          return {
            ...comment,
            reactions: comment.reactions.map(r =>
              r.emoji === emoji
                ? { ...r, count: r.count + 1, users: [...r.users, 'current-user'] }
                : r
            )
          }
        } else {
          return {
            ...comment,
            reactions: [...comment.reactions, { emoji, count: 1, users: ['current-user'] }]
          }
        }
      }
      return comment
    }))
  }, [])

  const filteredComments = comments.filter(comment => {
    if (filterType !== 'all' && comment.type !== filterType) return false
    if (searchQuery && !comment.content.toLowerCase().includes(searchQuery.toLowerCase())) return false
    return true
  })

  const getStatusIndicator = (status: TeamMember['status']) => {
    switch (status) {
      case 'online': return 'bg-green-500'
      case 'away': return 'bg-yellow-500'
      case 'offline': return 'bg-gray-400'
      default: return 'bg-gray-400'
    }
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 现代化标题区域 */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/10 via-purple-600/10 to-pink-600/10 rounded-3xl blur-3xl" />
        <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg">
                <Users className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  协作讨论中心
                </h1>
                <p className="text-gray-600 mt-1">实时团队协作，智能讨论管理</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onInviteUser?.('')}
                className="border-blue-200 hover:border-blue-400 hover:bg-blue-50 transition-all duration-300"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                邀请成员
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-purple-200 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300"
              >
                <Settings className="h-4 w-4 mr-2" />
                设置
              </Button>
            </div>
          </div>

          {/* 搜索和筛选 */}
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex-1 min-w-[300px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索讨论内容..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white/80 border-gray-200 hover:border-gray-400 transition-colors duration-300"
                />
              </div>
            </div>
            
            <Select value={filterType} onValueChange={(value: any) => setFilterType(value)}>
              <SelectTrigger className="w-36 bg-white/80 border-gray-200 hover:border-gray-400 transition-colors duration-300">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部类型</SelectItem>
                <SelectItem value="comment">评论</SelectItem>
                <SelectItem value="suggestion">建议</SelectItem>
                <SelectItem value="question">问题</SelectItem>
                <SelectItem value="insight">洞察</SelectItem>
                <SelectItem value="announcement">公告</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-1">
          <TabsTrigger value="discussions" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <MessageSquare className="h-4 w-4 mr-2" />
            讨论 ({filteredComments.length})
          </TabsTrigger>
          <TabsTrigger value="team" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <Users className="h-4 w-4 mr-2" />
            团队 ({teamMembers.length})
          </TabsTrigger>
          <TabsTrigger value="activity" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <Bell className="h-4 w-4 mr-2" />
            动态
          </TabsTrigger>
        </TabsList>

        {/* 讨论区域 */}
        <TabsContent value="discussions" className="space-y-6">
          {/* 新评论输入区域 */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src="/avatars/current-user.jpg" />
                    <AvatarFallback>我</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <Select value={commentType} onValueChange={(value: any) => setCommentType(value)}>
                      <SelectTrigger className="w-32 bg-white/80 border-gray-200">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(COMMENT_TYPES).map(([key, config]) => {
                          const IconComponent = config.icon
                          return (
                            <SelectItem key={key} value={key}>
                              <div className="flex items-center gap-2">
                                <IconComponent className={`h-4 w-4 ${config.color}`} />
                                {config.label}
                              </div>
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="relative">
                  <Textarea
                    ref={textareaRef}
                    placeholder="分享你的想法、建议或问题..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="min-h-[120px] bg-white/80 border-gray-200 hover:border-gray-400 focus:border-blue-400 transition-colors duration-300 resize-none"
                  />
                  <div className="absolute bottom-3 left-3 flex items-center gap-2">
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Smile className="h-4 w-4 text-gray-400" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Paperclip className="h-4 w-4 text-gray-400" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <Image className="h-4 w-4 text-gray-400" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                      <AtSign className="h-4 w-4 text-gray-400" />
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-500">
                    支持 Markdown 格式 • 使用 @ 提及团队成员
                  </div>
                  <Button
                    onClick={handleSubmitComment}
                    disabled={!newComment.trim()}
                    className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    发布
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 评论列表 */}
          <div className="space-y-4">
            {filteredComments.length === 0 ? (
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-12">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                    <MessageSquare className="h-8 w-8 text-gray-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无讨论</h3>
                  <p className="text-gray-600 mb-4">成为第一个发起讨论的人吧！</p>
                </div>
              </Card>
            ) : (
              filteredComments.map((comment) => {
                const typeConfig = COMMENT_TYPES[comment.type]
                const TypeIcon = typeConfig.icon

                return (
                  <Card key={comment.id} className={`group border-0 shadow-xl hover:shadow-2xl transition-all duration-300 bg-white/80 backdrop-blur-xl ${comment.isPinned ? 'ring-2 ring-yellow-200' : ''}`}>
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* 评论头部 */}
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-4 flex-1">
                            <div className="relative">
                              <Avatar className="h-12 w-12">
                                <AvatarImage src={comment.author.avatar} />
                                <AvatarFallback>{comment.author.name[0]}</AvatarFallback>
                              </Avatar>
                              <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${getStatusIndicator(comment.author.status)}`} />
                            </div>

                            <div className="flex-1">
                              <div className="flex items-center gap-3 mb-2">
                                <h4 className="font-semibold text-gray-900">{comment.author.name}</h4>
                                <Badge variant="secondary" className="text-xs">
                                  {comment.author.role}
                                </Badge>
                                <Badge className={`${typeConfig.bg} ${typeConfig.color} ${typeConfig.border} text-xs`}>
                                  <TypeIcon className="h-3 w-3 mr-1" />
                                  {typeConfig.label}
                                </Badge>
                                {comment.isPinned && (
                                  <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200 text-xs">
                                    <Pin className="h-3 w-3 mr-1" />
                                    置顶
                                  </Badge>
                                )}
                              </div>

                              <p className="text-gray-700 leading-relaxed mb-3">{comment.content}</p>

                              {/* 标签 */}
                              {comment.tags.length > 0 && (
                                <div className="flex items-center gap-2 mb-3">
                                  {comment.tags.map((tag, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">
                                      <Hash className="h-3 w-3 mr-1" />
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}

                              {/* 反应表情 */}
                              {comment.reactions.length > 0 && (
                                <div className="flex items-center gap-2 mb-3">
                                  {comment.reactions.map((reaction, index) => (
                                    <Button
                                      key={index}
                                      variant="outline"
                                      size="sm"
                                      className="h-8 px-3 bg-white/60 hover:bg-white/80 border-gray-200 hover:border-gray-400 transition-all duration-200"
                                      onClick={() => addReaction(comment.id, reaction.emoji)}
                                    >
                                      <span className="mr-1">{reaction.emoji}</span>
                                      <span className="text-xs">{reaction.count}</span>
                                    </Button>
                                  ))}
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600"
                                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                </div>
                              )}

                              {/* 操作按钮 */}
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleLike(comment.id)}
                                  className={`h-8 px-3 hover:bg-gray-100 ${comment.liked ? 'text-red-500' : 'text-gray-500'}`}
                                >
                                  <ThumbsUp className="h-4 w-4 mr-1" />
                                  {comment.likes}
                                </Button>

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setReplyingTo(comment.id)}
                                  className="h-8 px-3 hover:bg-gray-100 text-gray-500"
                                >
                                  <Reply className="h-4 w-4 mr-1" />
                                  回复
                                </Button>

                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleBookmark(comment.id)}
                                  className={`h-8 px-3 hover:bg-gray-100 ${comment.bookmarked ? 'text-yellow-500' : 'text-gray-500'}`}
                                >
                                  <Bookmark className="h-4 w-4 mr-1" />
                                  收藏
                                </Button>

                                <span className="text-xs text-gray-400">
                                  {new Date(comment.timestamp).toLocaleString('zh-CN')}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* 更多操作 */}
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* 回复输入框 */}
                        {replyingTo === comment.id && (
                          <div className="ml-16 space-y-3 p-4 bg-gray-50/80 rounded-xl">
                            <Textarea
                              placeholder="写下你的回复..."
                              value={replyContent}
                              onChange={(e) => setReplyContent(e.target.value)}
                              className="min-h-[80px] bg-white/80 border-gray-200"
                            />
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                onClick={() => handleSubmitReply(comment.id)}
                                disabled={!replyContent.trim()}
                                className="bg-blue-500 hover:bg-blue-600 text-white"
                              >
                                <Send className="h-3 w-3 mr-1" />
                                回复
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setReplyingTo(null)}
                              >
                                取消
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* 回复列表 */}
                        {comment.replies.length > 0 && (
                          <div className="ml-16 space-y-3">
                            {comment.replies.map((reply) => (
                              <div key={reply.id} className="p-4 bg-gray-50/60 rounded-xl">
                                <div className="flex items-start gap-3">
                                  <Avatar className="h-8 w-8">
                                    <AvatarImage src={reply.author.avatar} />
                                    <AvatarFallback>{reply.author.name[0]}</AvatarFallback>
                                  </Avatar>
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <span className="font-medium text-sm text-gray-900">{reply.author.name}</span>
                                      <span className="text-xs text-gray-500">
                                        {new Date(reply.timestamp).toLocaleString('zh-CN')}
                                      </span>
                                    </div>
                                    <p className="text-sm text-gray-700">{reply.content}</p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )
              })
            )}
          </div>
        </TabsContent>

        {/* 团队成员 */}
        <TabsContent value="team" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {teamMembers.map((member) => (
              <Card key={member.id} className="border-0 shadow-xl bg-white/80 backdrop-blur-xl hover:shadow-2xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={member.avatar} />
                        <AvatarFallback>{member.name[0]}</AvatarFallback>
                      </Avatar>
                      <div className={`absolute -bottom-1 -right-1 w-5 h-5 rounded-full border-2 border-white ${getStatusIndicator(member.status)}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{member.name}</h3>
                      <p className="text-sm text-gray-600">{member.role}</p>
                      <p className="text-xs text-gray-500">{member.department}</p>
                      {member.status === 'offline' && member.lastSeen && (
                        <p className="text-xs text-gray-400 mt-1">最后在线: {member.lastSeen}</p>
                      )}
                    </div>
                  </div>
                  <div className="mt-4 flex items-center gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <MessageCircle className="h-3 w-3 mr-1" />
                      私聊
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <AtSign className="h-3 w-3 mr-1" />
                      提及
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* 活动动态 */}
        <TabsContent value="activity" className="space-y-6">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
                <Bell className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">活动动态</h3>
              <p className="text-gray-600">实时跟踪团队活动和项目进展</p>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
