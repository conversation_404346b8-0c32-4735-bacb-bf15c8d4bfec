/**
 * 现代化洞察生成组件
 * AI驱动的智能洞察生成，具有现代化UI设计和丰富的交互体验
 */

'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { 
  Brain, 
  Lightbulb, 
  Sparkles,
  Zap,
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Share2,
  Bookmark,
  RefreshCw,
  Wand2,
  Eye,
  Filter,
  Download,
  MoreHorizontal,
  ArrowRight,
  Clock,
  User,
  Award,
  Layers,
  Cpu,
  Database,
  Settings
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar'
import { Experiment } from '../../types/experiment'

interface AIInsight {
  id: string
  type: 'breakthrough' | 'optimization' | 'warning' | 'discovery' | 'recommendation'
  title: string
  description: string
  confidence: number
  impact: 'critical' | 'high' | 'medium' | 'low'
  category: 'performance' | 'methodology' | 'data' | 'infrastructure' | 'business'
  actionable: boolean
  recommendations: string[]
  evidence: string[]
  tags: string[]
  generatedAt: string
  aiModel: string
  processingTime: number
  liked?: boolean
  bookmarked?: boolean
  shared?: boolean
  priority: number
}

interface ModernInsightsGeneratorProps {
  experiment: Experiment
  onSaveInsight?: (insight: AIInsight) => void
  onShareInsight?: (insight: AIInsight) => void
  onApplyRecommendation?: (recommendation: string) => void
  className?: string
}

// 现代化配色和渐变
const INSIGHT_THEMES = {
  breakthrough: {
    gradient: 'from-emerald-500 to-teal-600',
    bg: 'from-emerald-50 to-teal-50',
    border: 'border-l-emerald-500',
    icon: CheckCircle2,
    color: 'text-emerald-600'
  },
  optimization: {
    gradient: 'from-blue-500 to-indigo-600',
    bg: 'from-blue-50 to-indigo-50',
    border: 'border-l-blue-500',
    icon: Zap,
    color: 'text-blue-600'
  },
  warning: {
    gradient: 'from-amber-500 to-orange-600',
    bg: 'from-amber-50 to-orange-50',
    border: 'border-l-amber-500',
    icon: AlertTriangle,
    color: 'text-amber-600'
  },
  discovery: {
    gradient: 'from-purple-500 to-pink-600',
    bg: 'from-purple-50 to-pink-50',
    border: 'border-l-purple-500',
    icon: Sparkles,
    color: 'text-purple-600'
  },
  recommendation: {
    gradient: 'from-violet-500 to-purple-600',
    bg: 'from-violet-50 to-purple-50',
    border: 'border-l-violet-500',
    icon: Target,
    color: 'text-violet-600'
  }
}

export default function ModernInsightsGenerator({
  experiment,
  onSaveInsight,
  onShareInsight,
  onApplyRecommendation,
  className
}: ModernInsightsGeneratorProps) {
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedImpact, setSelectedImpact] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'priority' | 'confidence' | 'impact' | 'recent'>('priority')

  // 模拟高质量洞察数据
  const mockInsights: AIInsight[] = [
    {
      id: '1',
      type: 'breakthrough',
      title: '🚀 模型性能突破性提升',
      description: '通过优化注意力机制和引入残差连接，模型在测试集上的准确率提升了12.3%，达到了业界领先水平。这一突破为后续研究奠定了坚实基础。',
      confidence: 0.96,
      impact: 'critical',
      category: 'performance',
      actionable: true,
      recommendations: [
        '立即将优化后的模型部署到生产环境',
        '准备技术论文发表，分享研究成果',
        '基于此突破开展下一阶段的研究计划'
      ],
      evidence: [
        '测试准确率从75.2%提升至87.5%',
        '推理速度提升35%，延迟降低至120ms',
        '在5个不同数据集上均表现优异'
      ],
      tags: ['突破性进展', '性能优化', '注意力机制', '生产就绪'],
      generatedAt: '2025-01-31T10:30:00Z',
      aiModel: 'GPT-4 Turbo',
      processingTime: 2.3,
      liked: true,
      bookmarked: true,
      priority: 10
    },
    {
      id: '2',
      type: 'optimization',
      title: '⚡ 数据处理流程优化机会',
      description: '发现数据预处理阶段存在冗余操作，通过并行化处理和缓存机制，可以将整体训练时间减少40%。',
      confidence: 0.89,
      impact: 'high',
      category: 'infrastructure',
      actionable: true,
      recommendations: [
        '实施数据预处理并行化',
        '建立智能缓存系统',
        '优化数据加载器配置'
      ],
      evidence: [
        '当前预处理占用总时间的60%',
        '并行化测试显示4倍速度提升',
        '缓存命中率可达85%'
      ],
      tags: ['性能优化', '数据处理', '并行化', '缓存'],
      generatedAt: '2025-01-31T09:45:00Z',
      aiModel: 'Claude-3.5 Sonnet',
      processingTime: 1.8,
      liked: false,
      bookmarked: true,
      priority: 8
    },
    {
      id: '3',
      type: 'discovery',
      title: '🔍 数据分布异常发现',
      description: '在深入分析中发现训练数据存在隐藏的分布偏差，这可能影响模型的泛化能力。建议进行数据重新平衡。',
      confidence: 0.82,
      impact: 'medium',
      category: 'data',
      actionable: true,
      recommendations: [
        '执行数据分布分析',
        '实施数据重采样策略',
        '增加数据增强技术'
      ],
      evidence: [
        '类别A数据占比过高(65%)',
        '地理分布不均匀',
        '时间序列存在季节性偏差'
      ],
      tags: ['数据质量', '分布偏差', '数据平衡', '泛化能力'],
      generatedAt: '2025-01-31T08:20:00Z',
      aiModel: 'GPT-4 Turbo',
      processingTime: 3.1,
      liked: true,
      bookmarked: false,
      priority: 6
    }
  ]

  useEffect(() => {
    // 模拟初始加载
    setLoading(true)
    setTimeout(() => {
      setInsights(mockInsights)
      setLoading(false)
    }, 2000)
  }, [])

  const generateNewInsights = useCallback(async () => {
    setGenerating(true)
    
    // 模拟AI生成过程
    setTimeout(() => {
      const newInsight: AIInsight = {
        id: Date.now().toString(),
        type: 'recommendation',
        title: '🎯 超参数调优建议',
        description: '基于贝叶斯优化分析，建议调整学习率和批次大小的组合，预计可提升模型收敛速度30%。',
        confidence: 0.78,
        impact: 'medium',
        category: 'methodology',
        actionable: true,
        recommendations: [
          '学习率调整至0.001',
          '批次大小增加至128',
          '使用余弦退火调度器'
        ],
        evidence: [
          '当前配置收敛较慢',
          '贝叶斯优化推荐参数组合',
          '类似实验中效果显著'
        ],
        tags: ['超参数优化', '贝叶斯优化', '收敛速度'],
        generatedAt: new Date().toISOString(),
        aiModel: 'GPT-4 Turbo',
        processingTime: 1.5,
        liked: false,
        bookmarked: false,
        priority: 7
      }
      
      setInsights(prev => [newInsight, ...prev])
      setGenerating(false)
    }, 3000)
  }, [])

  const toggleLike = (id: string) => {
    setInsights(prev => prev.map(insight =>
      insight.id === id ? { ...insight, liked: !insight.liked } : insight
    ))
  }

  const toggleBookmark = (id: string) => {
    setInsights(prev => prev.map(insight =>
      insight.id === id ? { ...insight, bookmarked: !insight.bookmarked } : insight
    ))
  }

  const getImpactBadge = (impact: string) => {
    const styles = {
      critical: 'bg-red-100 text-red-800 border-red-200 shadow-red-100',
      high: 'bg-orange-100 text-orange-800 border-orange-200 shadow-orange-100',
      medium: 'bg-yellow-100 text-yellow-800 border-yellow-200 shadow-yellow-100',
      low: 'bg-green-100 text-green-800 border-green-200 shadow-green-100'
    }
    
    const labels = {
      critical: '关键影响',
      high: '高影响',
      medium: '中等影响',
      low: '低影响'
    }
    
    return (
      <Badge className={`${styles[impact as keyof typeof styles]} shadow-sm font-medium`}>
        {labels[impact as keyof typeof labels]}
      </Badge>
    )
  }

  const filteredAndSortedInsights = insights
    .filter(insight => {
      if (selectedCategory !== 'all' && insight.category !== selectedCategory) return false
      if (selectedImpact !== 'all' && insight.impact !== selectedImpact) return false
      return true
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'priority': return b.priority - a.priority
        case 'confidence': return b.confidence - a.confidence
        case 'impact': 
          const impactOrder = { critical: 4, high: 3, medium: 2, low: 1 }
          return impactOrder[b.impact] - impactOrder[a.impact]
        case 'recent': return new Date(b.generatedAt).getTime() - new Date(a.generatedAt).getTime()
        default: return 0
      }
    })

  if (loading) {
    return (
      <div className={`space-y-8 ${className}`}>
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-indigo-600/10 rounded-3xl blur-3xl" />
          <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-12">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full mb-6 shadow-2xl">
                <Brain className="h-10 w-10 text-white animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-3">
                AI正在深度分析实验数据
              </h3>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                我们的AI系统正在运用先进的机器学习算法，从多个维度分析您的实验数据，生成有价值的洞察和建议
              </p>
              <div className="space-y-4">
                <Progress value={75} className="w-80 mx-auto h-3" />
                <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                    数据预处理
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                    模式识别
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                    洞察生成
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 现代化标题区域 */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-indigo-600/10 rounded-3xl blur-3xl" />
        <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl shadow-lg">
                <Brain className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  AI洞察生成中心
                </h1>
                <p className="text-gray-600 mt-1">智能分析实验数据，生成可执行的深度洞察</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                onClick={generateNewInsights}
                disabled={generating}
                className="bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                {generating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Wand2 className="h-4 w-4 mr-2" />
                    生成新洞察
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="border-purple-200 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                导出报告
              </Button>
            </div>
          </div>

          {/* 筛选和排序控制 */}
          <div className="flex items-center gap-6 flex-wrap">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">类别：</span>
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-36 bg-white/80 border-purple-200 hover:border-purple-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类别</SelectItem>
                  <SelectItem value="performance">性能优化</SelectItem>
                  <SelectItem value="methodology">方法论</SelectItem>
                  <SelectItem value="data">数据质量</SelectItem>
                  <SelectItem value="infrastructure">基础设施</SelectItem>
                  <SelectItem value="business">业务价值</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">影响级别：</span>
              <Select value={selectedImpact} onValueChange={setSelectedImpact}>
                <SelectTrigger className="w-32 bg-white/80 border-blue-200 hover:border-blue-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部级别</SelectItem>
                  <SelectItem value="critical">关键影响</SelectItem>
                  <SelectItem value="high">高影响</SelectItem>
                  <SelectItem value="medium">中等影响</SelectItem>
                  <SelectItem value="low">低影响</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">排序：</span>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-32 bg-white/80 border-indigo-200 hover:border-indigo-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="priority">优先级</SelectItem>
                  <SelectItem value="confidence">置信度</SelectItem>
                  <SelectItem value="impact">影响程度</SelectItem>
                  <SelectItem value="recent">最新生成</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* 洞察统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 to-teal-50 hover:shadow-2xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-emerald-700 mb-1">总洞察数</p>
                <p className="text-3xl font-bold text-emerald-900">{insights.length}</p>
              </div>
              <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg">
                <Lightbulb className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-xl bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-2xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-700 mb-1">可执行建议</p>
                <p className="text-3xl font-bold text-blue-900">
                  {insights.filter(i => i.actionable).length}
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                <Target className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-xl bg-gradient-to-br from-purple-50 to-pink-50 hover:shadow-2xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-700 mb-1">平均置信度</p>
                <p className="text-3xl font-bold text-purple-900">
                  {insights.length > 0 ? Math.round(insights.reduce((acc, i) => acc + i.confidence, 0) / insights.length * 100) : 0}%
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg">
                <Award className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-xl bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-2xl transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-amber-700 mb-1">高影响洞察</p>
                <p className="text-3xl font-bold text-amber-900">
                  {insights.filter(i => i.impact === 'critical' || i.impact === 'high').length}
                </p>
              </div>
              <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl shadow-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 洞察卡片列表 */}
      <div className="space-y-6">
        {filteredAndSortedInsights.length === 0 ? (
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-12">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gray-100 rounded-full mb-4">
                <Eye className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">暂无匹配的洞察</h3>
              <p className="text-gray-600 mb-4">尝试调整筛选条件或生成新的洞察</p>
              <Button onClick={generateNewInsights} disabled={generating}>
                <Wand2 className="h-4 w-4 mr-2" />
                生成洞察
              </Button>
            </div>
          </Card>
        ) : (
          filteredAndSortedInsights.map((insight) => {
            const theme = INSIGHT_THEMES[insight.type]
            const IconComponent = theme.icon

            return (
              <Card key={insight.id} className={`group border-0 shadow-xl hover:shadow-2xl transition-all duration-500 hover:scale-[1.02] bg-gradient-to-r ${theme.bg} border-l-4 ${theme.border}`}>
                <CardContent className="p-8">
                  <div className="space-y-6">
                    {/* 洞察头部 */}
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4 flex-1">
                        <div className={`p-3 bg-gradient-to-br ${theme.gradient} rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300`}>
                          <IconComponent className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-xl font-bold text-gray-900">{insight.title}</h3>
                            {getImpactBadge(insight.impact)}
                          </div>
                          <p className="text-gray-700 leading-relaxed mb-4">{insight.description}</p>

                          {/* 置信度和元数据 */}
                          <div className="flex items-center gap-6 text-sm text-gray-600">
                            <div className="flex items-center gap-2">
                              <Award className="h-4 w-4" />
                              <span>置信度: {Math.round(insight.confidence * 100)}%</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              <span>处理时间: {insight.processingTime}s</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Cpu className="h-4 w-4" />
                              <span>{insight.aiModel}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center gap-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleLike(insight.id)}
                          className={`hover:bg-white/60 ${insight.liked ? 'text-red-500' : 'text-gray-400'}`}
                        >
                          <ThumbsUp className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleBookmark(insight.id)}
                          className={`hover:bg-white/60 ${insight.bookmarked ? 'text-yellow-500' : 'text-gray-400'}`}
                        >
                          <Bookmark className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onShareInsight?.(insight)}
                          className="hover:bg-white/60 text-gray-400"
                        >
                          <Share2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="hover:bg-white/60 text-gray-400"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    {/* 置信度进度条 */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="font-medium text-gray-700">AI置信度评估</span>
                        <span className="text-gray-600">{Math.round(insight.confidence * 100)}%</span>
                      </div>
                      <Progress
                        value={insight.confidence * 100}
                        className="h-2"
                        style={{
                          background: `linear-gradient(to right, ${theme.gradient.split(' ')[1]} 0%, ${theme.gradient.split(' ')[3]}40 100%)`
                        }}
                      />
                    </div>

                    {/* 可执行建议 */}
                    {insight.actionable && insight.recommendations.length > 0 && (
                      <div className="bg-white/60 rounded-xl p-6 space-y-4">
                        <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                          <Target className="h-5 w-5 text-blue-600" />
                          可执行建议
                        </h4>
                        <div className="space-y-3">
                          {insight.recommendations.map((rec, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-white/80 rounded-lg hover:bg-white transition-colors duration-200">
                              <div className="flex items-center gap-3">
                                <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                  <span className="text-xs font-medium text-blue-600">{index + 1}</span>
                                </div>
                                <span className="text-gray-700">{rec}</span>
                              </div>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onApplyRecommendation?.(rec)}
                                className="border-blue-200 hover:border-blue-400 hover:bg-blue-50 text-blue-600"
                              >
                                <ArrowRight className="h-3 w-3 mr-1" />
                                应用
                              </Button>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 支持证据 */}
                    {insight.evidence.length > 0 && (
                      <div className="bg-white/40 rounded-xl p-6 space-y-4">
                        <h4 className="font-semibold text-gray-900 flex items-center gap-2">
                          <Database className="h-5 w-5 text-green-600" />
                          支持证据
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {insight.evidence.map((evidence, index) => (
                            <div key={index} className="flex items-start gap-3 p-3 bg-white/60 rounded-lg">
                              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                              <span className="text-sm text-gray-700">{evidence}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 标签和元数据 */}
                    <div className="flex items-center justify-between pt-4 border-t border-white/40">
                      <div className="flex items-center gap-2 flex-wrap">
                        {insight.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="bg-white/60 text-gray-700 hover:bg-white/80 transition-colors duration-200">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>优先级: {insight.priority}/10</span>
                        <span>{new Date(insight.generatedAt).toLocaleString('zh-CN')}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </div>
    </div>
  )
}
