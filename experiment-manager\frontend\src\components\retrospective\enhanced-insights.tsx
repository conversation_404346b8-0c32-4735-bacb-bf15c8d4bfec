/**
 * 增强版洞察生成组件
 * AI驱动的智能洞察和建议生成
 */

'use client'

import React, { useState, useEffect } from 'react'
import { 
  Brain, 
  Lightbulb, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle2, 
  Sparkles,
  Zap,
  Target,
  ArrowRight,
  RefreshCw,
  Wand2,
  Star,
  ThumbsUp,
  ThumbsDown,
  MessageSquare,
  Share2,
  Bookmark
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card'
import { Button } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Experiment } from '../../types/experiment'

interface EnhancedInsightsProps {
  experiment: Experiment
  onSaveInsight?: (insight: any) => void
  onShareInsight?: (insight: any) => void
}

interface AIInsight {
  id: string
  type: 'success' | 'optimization' | 'warning' | 'discovery'
  title: string
  description: string
  confidence: number
  impact: 'high' | 'medium' | 'low'
  category: 'performance' | 'methodology' | 'data' | 'infrastructure'
  actionable: boolean
  recommendations: string[]
  evidence: string[]
  tags: string[]
  generatedAt: string
  aiModel: string
  liked?: boolean
  bookmarked?: boolean
}

export default function EnhancedInsights({
  experiment,
  onSaveInsight,
  onShareInsight
}: EnhancedInsightsProps) {
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [loading, setLoading] = useState(false)
  const [generatingNew, setGeneratingNew] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // 简化的模拟数据
  const mockInsights: AIInsight[] = [
    {
      id: '1',
      type: 'success',
      title: '🎯 假设验证成功',
      description: '实验结果支持原始假设',
      confidence: 0.94,
      impact: 'high',
      category: 'performance',
      actionable: true,
      recommendations: ['部署到生产环境'],
      evidence: ['测试准确率: 87.5%'],
      tags: ['假设验证'],
      generatedAt: '2025-01-31T10:30:00Z',
      aiModel: 'GPT-4 Turbo',
      liked: true,
      bookmarked: true
    }
  ]

  useEffect(() => {
    // 模拟加载洞察
    setLoading(true)
    setTimeout(() => {
      setInsights(mockInsights)
      setLoading(false)
    }, 1500)
  }, [])

  const generateNewInsights = async () => {
    setGeneratingNew(true)
    // 模拟AI生成新洞察
    setTimeout(() => {
      const newInsight: AIInsight = {
        id: Date.now().toString(),
        type: 'optimization',
        title: '🚀 模型架构优化建议',
        description: '基于最新的分析，建议在模型中添加注意力机制，可能提升5-8%的性能。',
        confidence: 0.72,
        impact: 'medium',
        category: 'methodology',
        actionable: true,
        recommendations: [
          '在编码器层添加自注意力机制',
          '尝试多头注意力 (8个头)',
          '使用位置编码增强序列理解'
        ],
        evidence: [
          '当前模型在长序列上表现下降',
          '注意力机制在类似任务上效果显著',
          '计算开销增加约15%但性能提升明显'
        ],
        tags: ['架构优化', '注意力机制', '性能提升'],
        generatedAt: new Date().toISOString(),
        aiModel: 'GPT-4 Turbo',
        liked: false,
        bookmarked: false
      }
      setInsights(prev => [newInsight, ...prev])
      setGeneratingNew(false)
    }, 2000)
  }

  const toggleLike = (id: string) => {
    setInsights(prev => prev.map(insight =>
      insight.id === id ? { ...insight, liked: !insight.liked } : insight
    ))
  }

  const toggleBookmark = (id: string) => {
    setInsights(prev => prev.map(insight =>
      insight.id === id ? { ...insight, bookmarked: !insight.bookmarked } : insight
    ))
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle2 className="h-5 w-5 text-green-600" />
      case 'optimization': return <Zap className="h-5 w-5 text-blue-600" />
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-600" />
      case 'discovery': return <Sparkles className="h-5 w-5 text-purple-600" />
      default: return <Lightbulb className="h-5 w-5 text-gray-600" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'success': return 'border-l-green-500 bg-green-50'
      case 'optimization': return 'border-l-blue-500 bg-blue-50'
      case 'warning': return 'border-l-yellow-500 bg-yellow-50'
      case 'discovery': return 'border-l-purple-500 bg-purple-50'
      default: return 'border-l-gray-500 bg-gray-50'
    }
  }

  const getImpactBadge = (impact: string) => {
    switch (impact) {
      case 'high': return <Badge className="bg-red-100 text-red-800 border-red-200">高影响</Badge>
      case 'medium': return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">中等影响</Badge>
      case 'low': return <Badge className="bg-green-100 text-green-800 border-green-200">低影响</Badge>
      default: return <Badge variant="secondary">未知</Badge>
    }
  }

  const filteredInsights = selectedCategory === 'all' 
    ? insights 
    : insights.filter(insight => insight.category === selectedCategory)

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full mb-4">
            <Brain className="h-8 w-8 text-purple-600 animate-pulse" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">AI正在分析实验数据...</h3>
          <p className="text-gray-600 mb-4">请稍候，我们正在生成智能洞察和建议</p>
          <Progress value={75} className="w-64 mx-auto" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* 现代化顶部标题区域 */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/10 via-blue-600/10 to-indigo-600/10 rounded-2xl blur-3xl" />
        <div className="relative bg-white/80 backdrop-blur-xl rounded-2xl border border-white/20 shadow-2xl p-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-blue-600 rounded-xl shadow-lg">
                <Wand2 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  AI智能洞察
                </h1>
                <p className="text-gray-600 mt-1">基于深度学习的实验分析与建议生成</p>
              </div>
              <Badge className="bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 px-4 py-2 text-sm font-semibold">
                {insights.length} 条洞察
              </Badge>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={generateNewInsights}
                disabled={generatingNew}
                className="border-purple-200 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300"
              >
                {generatingNew ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    生成新洞察
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* 现代化分类标签 */}
          <div className="flex items-center gap-4">
            <span className="text-sm font-medium text-gray-700">筛选类别：</span>
            <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
              <TabsList className="bg-gradient-to-r from-gray-100 to-purple-50 p-2 rounded-xl shadow-inner">
                <TabsTrigger value="all" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-md transition-all duration-300">
                  全部
                </TabsTrigger>
                <TabsTrigger value="performance" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-md transition-all duration-300">
                  性能优化
                </TabsTrigger>
                <TabsTrigger value="methodology" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-md transition-all duration-300">
                  方法论
                </TabsTrigger>
                <TabsTrigger value="data" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-md transition-all duration-300">
                  数据质量
                </TabsTrigger>
                <TabsTrigger value="infrastructure" className="rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-md transition-all duration-300">
                  基础设施
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>
      </div>

      {/* 现代化洞察列表 */}
      <div className="space-y-4">
        {filteredInsights.map((insight) => (
          <Card key={insight.id} className={`border-l-4 ${getTypeColor(insight.type)} shadow-lg hover:shadow-xl transition-shadow duration-200`}>
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* 洞察头部 */}
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {getTypeIcon(insight.type)}
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{insight.title}</h3>
                      <p className="text-gray-700 leading-relaxed">{insight.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    {getImpactBadge(insight.impact)}
                    <Badge variant="outline" className="text-xs">
                      {(insight.confidence * 100).toFixed(0)}% 置信度
                    </Badge>
                  </div>
                </div>

                {/* 建议和证据 */}
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <Target className="h-4 w-4 text-blue-500" />
                      行动建议
                    </h4>
                    <ul className="space-y-1">
                      {insight.recommendations.map((rec, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                          <ArrowRight className="h-3 w-3 text-blue-500 mt-0.5 flex-shrink-0" />
                          {rec}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      支持证据
                    </h4>
                    <ul className="space-y-1">
                      {insight.evidence.map((evidence, index) => (
                        <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                          {evidence}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                {/* 标签和操作 */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center gap-2">
                    {insight.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleLike(insight.id)}
                      className={insight.liked ? 'text-red-600' : 'text-gray-400'}
                    >
                      <ThumbsUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleBookmark(insight.id)}
                      className={insight.bookmarked ? 'text-yellow-600' : 'text-gray-400'}
                    >
                      <Bookmark className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => onShareInsight?.(insight)}>
                      <Share2 className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* AI模型信息 */}
                <div className="text-xs text-gray-500 flex items-center justify-between pt-2 border-t border-gray-100">
                  <span>由 {insight.aiModel} 生成</span>
                  <span>{new Date(insight.generatedAt).toLocaleString('zh-CN')}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredInsights.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <Brain className="h-16 w-16 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">暂无此类别的洞察</p>
          <p className="text-sm">尝试生成新的洞察或切换其他类别</p>
        </div>
      )}
    </div>
  )
}
