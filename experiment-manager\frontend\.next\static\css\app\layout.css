/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[12].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src/app/layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.20%;descent-override: 22.48%;line-gap-override: 0.00%;size-adjust: 107.40%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 强制加载现代化样式 */
@layer utilities {
  .bg-gradient-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .text-gradient-primary {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

/* 确保渐变背景正确显示 */
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* 确保backdrop-blur正确显示 */
.backdrop-blur-sm {
  -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);
}

.backdrop-blur-md {
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
}

.backdrop-blur-lg {
  -webkit-backdrop-filter: blur(16px);
          backdrop-filter: blur(16px);
}

.backdrop-blur-xl {
  -webkit-backdrop-filter: blur(24px);
          backdrop-filter: blur(24px);
}

/* 现代化设计系统 - 全局样式 */
@layer base {
  :root {
    /* 现代化色彩系统 */
    --color-primary-50: #eff6ff;
    --color-primary-100: #dbeafe;
    --color-primary-200: #bfdbfe;
    --color-primary-300: #93c5fd;
    --color-primary-400: #60a5fa;
    --color-primary-500: #3b82f6;
    --color-primary-600: #2563eb;
    --color-primary-700: #1d4ed8;
    --color-primary-800: #1e40af;
    --color-primary-900: #1e3a8a;

    /* 渐变色系统 */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

    /* 现代化圆角系统 */
    --radius-xs: 0.125rem;
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;

    /* 动画时长 */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;

    /* 字体系统 */
    --font-display: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-body: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  }

  /* 深色主题支持 */
  @media (prefers-color-scheme: dark) {
    :root {
      --color-primary-50: #1e3a8a;
      --color-primary-100: #1e40af;
      --color-primary-200: #1d4ed8;
      --color-primary-300: #2563eb;
      --color-primary-400: #3b82f6;
      --color-primary-500: #60a5fa;
      --color-primary-600: #93c5fd;
      --color-primary-700: #bfdbfe;
      --color-primary-800: #dbeafe;
      --color-primary-900: #eff6ff;
    }
  }

  /* 全局基础样式 */
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* 现代化动画效果 */
  .animate-fade-in-up {
    animation: fadeInUp 0.5s ease-out;
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  /* 文本渐变效果 */
  .text-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 悬停效果增强 */
  .hover-lift {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  }

  /* 焦点环增强 */
  .focus-ring {
    @apply focus:outline-none focus:ring-4 focus:ring-blue-100 focus:border-blue-300;
  }
}

/* 实验流程可视化动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

/* 步骤动画延迟 */
.animation-delay-0 { animation-delay: 0ms; }
.animation-delay-100 { animation-delay: 100ms; }
.animation-delay-200 { animation-delay: 200ms; }
.animation-delay-300 { animation-delay: 300ms; }
.animation-delay-400 { animation-delay: 400ms; }
.animation-delay-500 { animation-delay: 500ms; }

/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[12].oneOf[12].use[3]!./src/styles/modern-theme.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/**
 * 现代化主题样式
 * Sean的界面升级方案 - 专业科研工具配色系统
 */

/* 科研主题配色变量 */
:root {
  /* 主色调 - 科技蓝 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* 成功色 - 实验成功 */
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-200: #a7f3d0;
  --success-300: #6ee7b7;
  --success-400: #34d399;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065f46;
  --success-900: #064e3b;

  /* 警告色 - 进行中状态 */
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* 错误色 - 实验失败 */
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* 紫色 - 复盘状态 */
  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-300: #d8b4fe;
  --purple-400: #c084fc;
  --purple-500: #a855f7;
  --purple-600: #9333ea;
  --purple-700: #7c3aed;
  --purple-800: #6b21a8;
  --purple-900: #581c87;

  /* 中性色 - 背景和文字 */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;

  /* 现代化渐变背景 */
  --gradient-primary: linear-gradient(135deg, var(--primary-600), var(--purple-600));
  --gradient-success: linear-gradient(135deg, var(--success-500), var(--success-600));
  --gradient-warning: linear-gradient(135deg, var(--warning-500), var(--warning-600));
  --gradient-background: linear-gradient(135deg, var(--neutral-50), var(--primary-50));
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

  /* 玻璃态效果 */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --glass-backdrop: blur(8px);

  /* 现代化阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-blue: 0 10px 15px -3px rgb(59 130 246 / 0.1), 0 4px 6px -4px rgb(59 130 246 / 0.1);
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.3);

  /* 圆角系统 */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

/* 现代化卡片样式 */
.modern-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--neutral-200);
  transition: all var(--duration-normal) ease;
}

.modern-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.modern-card-gradient {
  background: linear-gradient(135deg, white, var(--neutral-50));
}

/* 状态指示器样式 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid;
}

.status-pending {
  background-color: var(--neutral-100);
  color: var(--neutral-800);
  border-color: var(--neutral-200);
}

.status-running {
  background-color: var(--primary-50);
  color: var(--primary-700);
  border-color: var(--primary-200);
}

.status-completed {
  background-color: var(--success-50);
  color: var(--success-700);
  border-color: var(--success-200);
}

.status-failed {
  background-color: var(--error-50);
  color: var(--error-700);
  border-color: var(--error-200);
}

.status-reviewing {
  background-color: var(--purple-50);
  color: var(--purple-700);
  border-color: var(--purple-200);
}

.status-archived {
  background-color: var(--neutral-50);
  color: var(--neutral-600);
  border-color: var(--neutral-200);
}

/* 现代化按钮样式 */
.btn-primary-gradient {
  background: var(--gradient-primary);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all var(--duration-normal) ease;
  box-shadow: var(--shadow-sm);
}

.btn-primary-gradient:hover {
  box-shadow: var(--shadow-blue);
  transform: translateY(-1px);
}

/* 进度环样式 */
.progress-ring {
  transform: rotate(-90deg);
}

.progress-ring-circle {
  transition: stroke-dashoffset var(--duration-slow) ease-in-out;
  stroke-linecap: round;
}

/* 仪表板网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  grid-gap: 1.5rem;
  gap: 1.5rem;
}

/* 实验卡片网格 */
.experiments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  grid-gap: 1.5rem;
  gap: 1.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .experiments-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in-up {
  animation: fadeInUp var(--duration-normal) ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* 焦点样式 */
.focus-ring {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 文字渐变 */
.text-gradient-primary {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 现代化导航栏增强 */
.navbar-modern {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-sm);
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-search {
  position: relative;
  max-width: 400px;
  width: 100%;
}

.navbar-search input {
  width: 100%;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  background: rgba(255, 255, 255, 0.9);
  transition: all var(--duration-normal) ease;
}

.navbar-search input:focus {
  outline: none;
  border-color: var(--primary-300);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.navbar-search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--neutral-400);
}

/* 现代化横向导航栏样式 */
.horizontal-nav {
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--neutral-200);
  box-shadow: var(--shadow-lg);
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin: 0 0.25rem;
  border-radius: var(--radius-lg);
  color: var(--neutral-600);
  text-decoration: none;
  transition: all var(--duration-normal) ease;
  position: relative;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.5);
  color: var(--primary-700);
  transform: translateY(-2px);
}

.nav-item.active {
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.nav-item-icon {
  margin-right: 0.5rem;
  width: 1rem;
  height: 1rem;
}

/* 现代化表单组件 */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-700);
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: 0.875rem;
  transition: all var(--duration-normal) ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input.error {
  border-color: var(--error-400);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
  font-size: 0.75rem;
  color: var(--error-600);
  margin-top: 0.25rem;
}

.form-help {
  font-size: 0.75rem;
  color: var(--neutral-500);
  margin-top: 0.25rem;
}

/* 现代化选择器 */
.select-modern {
  position: relative;
}

.select-modern select {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: 0.875rem;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer;
  transition: all var(--duration-normal) ease;
}

.select-modern::after {
  content: '';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--neutral-400);
  pointer-events: none;
}

/* 现代化复选框和单选框 */
.checkbox-modern,
.radio-modern {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-modern input,
.radio-modern input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.checkbox-modern .checkmark,
.radio-modern .checkmark {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid var(--neutral-300);
  background: white;
  margin-right: 0.5rem;
  transition: all var(--duration-normal) ease;
}

.checkbox-modern .checkmark {
  border-radius: var(--radius-sm);
}

.radio-modern .checkmark {
  border-radius: 50%;
}

.checkbox-modern input:checked ~ .checkmark,
.radio-modern input:checked ~ .checkmark {
  background: var(--primary-500);
  border-color: var(--primary-500);
}

.checkbox-modern .checkmark::after {
  content: '';
  position: absolute;
  left: 4px;
  top: 1px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  opacity: 0;
  transition: opacity var(--duration-normal) ease;
}

.checkbox-modern input:checked ~ .checkmark::after {
  opacity: 1;
}

.radio-modern .checkmark::after {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: white;
  opacity: 0;
  transition: opacity var(--duration-normal) ease;
}

.radio-modern input:checked ~ .checkmark::after {
  opacity: 1;
}

/* 页面中使用的缺失样式类 */
.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--duration-normal) ease;
}

.icon-container:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.stats-card {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-lg);
  border: 1px solid;
  transition: all var(--duration-normal) ease;
}

.stats-value {
  font-size: 1.125rem;
  font-weight: 700;
}

.stats-label {
  font-size: 0.875rem;
  font-weight: 500;
}

.badge-modern {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-info {
  background-color: var(--primary-100);
  color: var(--primary-800);
  border: 1px solid var(--primary-200);
}

.search-modern {
  position: relative;
  flex: 1 1;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--neutral-400);
  z-index: 10;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  background: white;
  transition: all var(--duration-normal) ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-400);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-modern-primary {
  background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--duration-normal) ease;
  box-shadow: var(--shadow-md);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-modern-primary:hover {
  background: linear-gradient(135deg, var(--primary-700), var(--primary-800));
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.btn-modern-secondary {
  background: white;
  color: var(--neutral-700);
  border: 1px solid var(--neutral-300);
  border-radius: var(--radius-lg);
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--duration-normal) ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-modern-secondary:hover {
  background: var(--neutral-50);
  border-color: var(--primary-300);
  color: var(--primary-700);
  transform: translateY(-1px);
}

.btn-modern-ghost {
  background: transparent;
  color: var(--neutral-600);
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  padding: 0.75rem 1rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all var(--duration-normal) ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-modern-ghost:hover {
  background: var(--neutral-100);
  color: var(--neutral-800);
}

.dropdown-modern {
  background: white;
  border: 1px solid var(--neutral-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: 0.5rem;
  min-width: 200px;
}

.dropdown-open {
  animation: fadeInUp var(--duration-fast) ease-out;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--neutral-700);
  cursor: pointer;
  transition: all var(--duration-fast) ease;
}

.dropdown-item:hover {
  background: var(--neutral-100);
  color: var(--neutral-900);
}

