@echo off
echo.
echo ========================================
echo 🎨 现代化UI展示启动器
echo ========================================
echo.

echo 📁 当前目录: %CD%
echo.

echo 🔍 检查Node.js环境...
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js
    echo 📥 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js版本:
node --version
echo.

echo 🔍 检查npm环境...
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到npm
    pause
    exit /b 1
)

echo ✅ npm版本:
npm --version
echo.

echo 📦 检查依赖是否已安装...
if not exist "node_modules" (
    echo 📥 正在安装依赖...
    npm install
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖已存在
)
echo.

echo 🚀 启动开发服务器...
echo.
echo ========================================
echo 🌐 访问地址:
echo   主页: http://localhost:3000
echo   现代化UI展示: http://localhost:3000/modern-ui-showcase
echo ========================================
echo.
echo 💡 提示: 按 Ctrl+C 停止服务器
echo.

npm run dev
