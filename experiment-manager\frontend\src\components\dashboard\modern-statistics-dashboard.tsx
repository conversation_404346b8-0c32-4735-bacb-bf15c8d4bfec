/**
 * 现代化统计仪表板组件
 * 提供全面的数据可视化、实时监控和智能分析功能
 */

'use client'

import React, { useState, useMemo, useEffect } from 'react'
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Activity, 
  Users, 
  Clock, 
  Target, 
  Zap,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Award,
  AlertTriangle,
  CheckCircle2,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Settings,
  Maximize2,
  MoreHorizontal,
  PieChart,
  LineChart,
  BarChart,
  Gauge,
  Layers,
  Database,
  Cpu,
  HardDrive,
  Wifi,
  Battery,
  Shield,
  Globe,
  Server
} from 'lucide-react'
import {
  ResponsiveContainer,
  BarChart as RechartsBarChart,
  LineChart as RechartsLineChart,
  PieChart as RechartsPieChart,
  AreaChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Bar,
  Line,
  Area,
  Pie,
  Cell
} from 'recharts'
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card'
import { But<PERSON> } from '../ui/button'
import { Badge } from '../ui/badge'
import { Progress } from '../ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select'
import { Experiment } from '../../types/experiment'

interface DashboardMetric {
  id: string
  name: string
  value: number
  unit: string
  change: number
  changeType: 'increase' | 'decrease' | 'neutral'
  trend: number[]
  target?: number
  status: 'excellent' | 'good' | 'warning' | 'critical'
  category: 'performance' | 'efficiency' | 'quality' | 'resource'
}

interface ModernStatisticsDashboardProps {
  experiment?: Experiment
  onExportReport?: (format: string) => void
  onRefreshData?: () => void
  className?: string
}

// 现代化配色方案
const COLORS = {
  primary: ['#6366f1', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981', '#06b6d4', '#ef4444', '#84cc16'],
  gradients: [
    'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
    'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
    'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
    'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
    'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
    'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
  ]
}

export default function ModernStatisticsDashboard({
  experiment,
  onExportReport,
  onRefreshData,
  className
}: ModernStatisticsDashboardProps) {
  const [timeRange, setTimeRange] = useState<'1h' | '24h' | '7d' | '30d' | '90d'>('24h')
  const [metricCategory, setMetricCategory] = useState<'all' | 'performance' | 'efficiency' | 'quality' | 'resource'>('all')
  const [refreshing, setRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'performance' | 'analytics' | 'system'>('overview')

  // 模拟实时仪表板数据
  const dashboardData = useMemo(() => ({
    overview: {
      totalExperiments: 1247,
      activeExperiments: 23,
      successRate: 94.2,
      avgDuration: 42.8,
      teamMembers: 18,
      totalInsights: 456,
      resolvedIssues: 127,
      systemUptime: 99.8
    },
    metrics: [
      {
        id: 'success_rate',
        name: '实验成功率',
        value: 94.2,
        unit: '%',
        change: 3.8,
        changeType: 'increase' as const,
        trend: [89, 91, 92, 93, 94, 95, 94.2],
        target: 90,
        status: 'excellent' as const,
        category: 'performance' as const
      },
      {
        id: 'avg_duration',
        name: '平均执行时长',
        value: 42.8,
        unit: '分钟',
        change: -12.5,
        changeType: 'decrease' as const,
        trend: [58, 55, 52, 48, 45, 43, 42.8],
        target: 45,
        status: 'excellent' as const,
        category: 'efficiency' as const
      },
      {
        id: 'data_quality',
        name: '数据质量分数',
        value: 96.7,
        unit: '分',
        change: 2.3,
        changeType: 'increase' as const,
        trend: [92, 93, 94, 95, 96, 97, 96.7],
        target: 95,
        status: 'excellent' as const,
        category: 'quality' as const
      },
      {
        id: 'resource_usage',
        name: '资源利用率',
        value: 78.4,
        unit: '%',
        change: -5.2,
        changeType: 'decrease' as const,
        trend: [85, 84, 82, 80, 79, 78, 78.4],
        target: 80,
        status: 'warning' as const,
        category: 'resource' as const
      },
      {
        id: 'throughput',
        name: '处理吞吐量',
        value: 1847,
        unit: '次/小时',
        change: 15.7,
        changeType: 'increase' as const,
        trend: [1520, 1580, 1650, 1720, 1780, 1820, 1847],
        target: 1500,
        status: 'excellent' as const,
        category: 'performance' as const
      },
      {
        id: 'error_rate',
        name: '错误率',
        value: 0.8,
        unit: '%',
        change: -0.3,
        changeType: 'decrease' as const,
        trend: [1.5, 1.3, 1.1, 1.0, 0.9, 0.8, 0.8],
        target: 1.0,
        status: 'excellent' as const,
        category: 'quality' as const
      }
    ],
    timeSeriesData: [
      { time: '00:00', experiments: 45, success: 42, failed: 3, cpu: 65, memory: 72 },
      { time: '04:00', experiments: 38, success: 36, failed: 2, cpu: 58, memory: 68 },
      { time: '08:00', experiments: 67, success: 64, failed: 3, cpu: 78, memory: 82 },
      { time: '12:00', experiments: 89, success: 85, failed: 4, cpu: 85, memory: 88 },
      { time: '16:00', experiments: 76, success: 73, failed: 3, cpu: 82, memory: 85 },
      { time: '20:00', experiments: 54, success: 51, failed: 3, cpu: 70, memory: 75 }
    ],
    categoryData: [
      { name: '机器学习', value: 42, color: '#6366f1' },
      { name: '深度学习', value: 28, color: '#8b5cf6' },
      { name: '数据分析', value: 18, color: '#ec4899' },
      { name: '模型优化', value: 12, color: '#f59e0b' }
    ],
    systemMetrics: [
      { name: 'CPU使用率', value: 68, max: 100, status: 'good', icon: Cpu },
      { name: '内存使用率', value: 74, max: 100, status: 'good', icon: HardDrive },
      { name: '磁盘I/O', value: 45, max: 100, status: 'excellent', icon: Database },
      { name: '网络延迟', value: 23, max: 100, status: 'excellent', icon: Wifi },
      { name: '系统负载', value: 82, max: 100, status: 'warning', icon: Server },
      { name: '可用性', value: 99.8, max: 100, status: 'excellent', icon: Shield }
    ],
    recentActivity: [
      { type: 'success', message: '实验 "深度学习模型优化" 成功完成', time: '2分钟前', icon: CheckCircle2 },
      { type: 'info', message: '新增3个团队成员加入项目', time: '15分钟前', icon: Users },
      { type: 'warning', message: '系统资源使用率达到80%', time: '32分钟前', icon: AlertTriangle },
      { type: 'success', message: '数据质量检查通过', time: '1小时前', icon: Database },
      { type: 'info', message: '生成了12条新的AI洞察', time: '2小时前', icon: Zap }
    ]
  }), [timeRange])

  const handleRefresh = async () => {
    setRefreshing(true)
    try {
      await onRefreshData?.()
      setTimeout(() => setRefreshing(false), 2000)
    } catch (error) {
      setRefreshing(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-emerald-600 bg-emerald-50 border-emerald-200'
      case 'good': return 'text-blue-600 bg-blue-50 border-blue-200'
      case 'warning': return 'text-amber-600 bg-amber-50 border-amber-200'
      case 'critical': return 'text-red-600 bg-red-50 border-red-200'
      default: return 'text-gray-600 bg-gray-50 border-gray-200'
    }
  }

  const getChangeIcon = (changeType: string) => {
    switch (changeType) {
      case 'increase': return <ArrowUpRight className="h-4 w-4 text-emerald-600" />
      case 'decrease': return <ArrowDownRight className="h-4 w-4 text-red-600" />
      default: return <Minus className="h-4 w-4 text-gray-600" />
    }
  }

  const filteredMetrics = dashboardData.metrics.filter(metric => {
    if (metricCategory === 'all') return true
    return metric.category === metricCategory
  })

  // 自定义Tooltip组件
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white/95 backdrop-blur-xl border border-white/20 rounded-xl shadow-2xl p-4 min-w-[200px]">
          <p className="font-semibold text-gray-900 mb-2">{label}</p>
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex items-center justify-between mb-1">
              <span className="text-sm text-gray-600">{entry.name}:</span>
              <span className="font-medium" style={{ color: entry.color }}>
                {entry.value}
              </span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* 现代化标题区域 */}
      <div className="relative">
        <div className="absolute inset-0 bg-gradient-to-r from-indigo-600/10 via-purple-600/10 to-pink-600/10 rounded-3xl blur-3xl" />
        <div className="relative bg-white/80 backdrop-blur-xl rounded-3xl border border-white/20 shadow-2xl p-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-4 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                <BarChart3 className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  统计仪表板
                </h1>
                <p className="text-gray-600 mt-1">实时监控系统性能，全方位数据洞察</p>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
                className="border-indigo-200 hover:border-indigo-400 hover:bg-indigo-50 transition-all duration-300"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                刷新数据
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onExportReport?.('pdf')}
                className="border-purple-200 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300"
              >
                <Download className="h-4 w-4 mr-2" />
                导出报告
              </Button>
            </div>
          </div>

          {/* 控制面板 */}
          <div className="flex items-center gap-6 flex-wrap">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">时间范围：</span>
              <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
                <SelectTrigger className="w-32 bg-white/80 border-indigo-200 hover:border-indigo-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1h">最近1小时</SelectItem>
                  <SelectItem value="24h">最近24小时</SelectItem>
                  <SelectItem value="7d">最近7天</SelectItem>
                  <SelectItem value="30d">最近30天</SelectItem>
                  <SelectItem value="90d">最近90天</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-gray-700">指标类别：</span>
              <Select value={metricCategory} onValueChange={(value: any) => setMetricCategory(value)}>
                <SelectTrigger className="w-36 bg-white/80 border-purple-200 hover:border-purple-400 transition-colors duration-300">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部指标</SelectItem>
                  <SelectItem value="performance">性能指标</SelectItem>
                  <SelectItem value="efficiency">效率指标</SelectItem>
                  <SelectItem value="quality">质量指标</SelectItem>
                  <SelectItem value="resource">资源指标</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* 核心指标概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* 总实验数 */}
        <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-blue-50 via-cyan-50 to-blue-100 hover:shadow-2xl transition-all duration-500 hover:scale-105">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-cyan-500/5" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <Activity className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 text-blue-600 text-xs font-medium">
                  <TrendingUp className="h-3 w-3" />
                  +127
                </div>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-blue-700 mb-1">总实验数</p>
              <p className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent">
                {dashboardData.overview.totalExperiments.toLocaleString()}
              </p>
            </div>
            <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-gradient-to-br from-blue-200/30 to-cyan-200/30 rounded-full blur-xl" />
          </CardContent>
        </Card>

        {/* 活跃实验 */}
        <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-emerald-50 via-green-50 to-teal-100 hover:shadow-2xl transition-all duration-500 hover:scale-105">
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-teal-500/5" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 text-emerald-600 text-xs font-medium">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse" />
                  实时
                </div>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-emerald-700 mb-1">活跃实验</p>
              <p className="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                {dashboardData.overview.activeExperiments}
              </p>
            </div>
            <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-gradient-to-br from-emerald-200/30 to-teal-200/30 rounded-full blur-xl" />
          </CardContent>
        </Card>

        {/* 成功率 */}
        <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100 hover:shadow-2xl transition-all duration-500 hover:scale-105">
          <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-violet-500/5" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-violet-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <Target className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 text-purple-600 text-xs font-medium">
                  <CheckCircle2 className="h-3 w-3" />
                  优秀
                </div>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-purple-700 mb-1">成功率</p>
              <p className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text text-transparent">
                {dashboardData.overview.successRate}%
              </p>
            </div>
            <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-gradient-to-br from-purple-200/30 to-violet-200/30 rounded-full blur-xl" />
          </CardContent>
        </Card>

        {/* 系统运行时间 */}
        <Card className="group relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-amber-50 via-orange-50 to-amber-100 hover:shadow-2xl transition-all duration-500 hover:scale-105">
          <div className="absolute inset-0 bg-gradient-to-br from-amber-500/5 to-orange-500/5" />
          <CardContent className="relative p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl shadow-lg group-hover:shadow-xl transition-shadow duration-300">
                <Shield className="h-6 w-6 text-white" />
              </div>
              <div className="text-right">
                <div className="flex items-center gap-1 text-amber-600 text-xs font-medium">
                  <Battery className="h-3 w-3" />
                  稳定
                </div>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-amber-700 mb-1">系统可用性</p>
              <p className="text-3xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent">
                {dashboardData.overview.systemUptime}%
              </p>
            </div>
            <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-gradient-to-br from-amber-200/30 to-orange-200/30 rounded-full blur-xl" />
          </CardContent>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <Tabs value={activeTab} onValueChange={(value: any) => setActiveTab(value)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4 bg-white/80 backdrop-blur-xl border border-white/20 rounded-2xl p-1">
          <TabsTrigger value="overview" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <BarChart3 className="h-4 w-4 mr-2" />
            概览
          </TabsTrigger>
          <TabsTrigger value="performance" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <TrendingUp className="h-4 w-4 mr-2" />
            性能
          </TabsTrigger>
          <TabsTrigger value="analytics" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <PieChart className="h-4 w-4 mr-2" />
            分析
          </TabsTrigger>
          <TabsTrigger value="system" className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-purple-600 data-[state=active]:text-white">
            <Server className="h-4 w-4 mr-2" />
            系统
          </TabsTrigger>
        </TabsList>

        {/* 概览标签页 */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* 主图表区域 */}
            <div className="lg:col-span-2">
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 text-xl">
                      <LineChart className="h-6 w-6 text-indigo-600" />
                      实验趋势分析
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Maximize2 className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={dashboardData.timeSeriesData} accessibilityLayer>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                        <XAxis dataKey="time" stroke="#64748b" fontSize={12} />
                        <YAxis stroke="#64748b" fontSize={12} />
                        <Tooltip content={<CustomTooltip />} />
                        <Legend />
                        <Area type="monotone" dataKey="experiments" stackId="1" stroke="#6366f1" fill="#6366f1" fillOpacity={0.6} name="总实验数" />
                        <Area type="monotone" dataKey="success" stackId="1" stroke="#10b981" fill="#10b981" fillOpacity={0.6} name="成功数" />
                      </AreaChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 侧边栏 - 关键指标 */}
            <div className="space-y-6">
              {/* 关键指标卡片 */}
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Gauge className="h-5 w-5 text-blue-600" />
                    关键指标
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {filteredMetrics.slice(0, 4).map((metric, index) => (
                    <div key={metric.id} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700">{metric.name}</span>
                        <div className="flex items-center gap-2">
                          {getChangeIcon(metric.changeType)}
                          <Badge className={getStatusColor(metric.status)} size="sm">
                            {metric.value}{metric.unit}
                          </Badge>
                        </div>
                      </div>
                      <div className="space-y-1">
                        <Progress
                          value={metric.target ? (metric.value / metric.target) * 100 : metric.value}
                          className="h-2"
                        />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>变化: {metric.change > 0 ? '+' : ''}{metric.change}%</span>
                          {metric.target && <span>目标: {metric.target}{metric.unit}</span>}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* 最近活动 */}
              <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Activity className="h-5 w-5 text-green-600" />
                    最近活动
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {dashboardData.recentActivity.map((activity, index) => {
                    const IconComponent = activity.icon
                    return (
                      <div key={index} className="flex items-start gap-3 p-3 bg-gray-50/80 rounded-lg hover:bg-gray-100/80 transition-colors duration-200">
                        <div className={`p-2 rounded-lg ${
                          activity.type === 'success' ? 'bg-green-100 text-green-600' :
                          activity.type === 'warning' ? 'bg-yellow-100 text-yellow-600' :
                          'bg-blue-100 text-blue-600'
                        }`}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-gray-900">{activity.message}</p>
                          <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                        </div>
                      </div>
                    )
                  })}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* 其他标签页占位符 */}
        <TabsContent value="performance" className="space-y-6">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-8">
            <div className="text-center">
              <TrendingUp className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">性能监控</h3>
              <p className="text-gray-600">实时监控系统性能指标和资源使用情况</p>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl p-8">
            <div className="text-center">
              <PieChart className="h-16 w-16 text-indigo-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">数据分析</h3>
              <p className="text-gray-600">深度分析实验数据，发现隐藏的模式和趋势</p>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {dashboardData.systemMetrics.map((metric, index) => {
              const IconComponent = metric.icon
              return (
                <Card key={metric.name} className="border-0 shadow-xl bg-white/80 backdrop-blur-xl hover:shadow-2xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className={`p-3 rounded-xl shadow-lg ${
                        metric.status === 'excellent' ? 'bg-gradient-to-br from-emerald-500 to-teal-600' :
                        metric.status === 'good' ? 'bg-gradient-to-br from-blue-500 to-indigo-600' :
                        metric.status === 'warning' ? 'bg-gradient-to-br from-amber-500 to-orange-600' :
                        'bg-gradient-to-br from-red-500 to-pink-600'
                      }`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900">{metric.name}</h3>
                        <p className="text-2xl font-bold text-gray-900">{metric.value}%</p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Progress value={metric.value} className="h-2" />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>当前使用</span>
                        <span>{metric.value}% / {metric.max}%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
