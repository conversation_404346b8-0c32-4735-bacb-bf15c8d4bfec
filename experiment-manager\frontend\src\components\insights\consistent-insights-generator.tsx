/**
 * 与现有风格一致的洞察生成组件
 * 使用内联样式，保持与项目整体设计的一致性
 */

'use client'

import React, { useState, useEffect } from 'react'
import { 
  Brain, 
  Lightbulb, 
  Zap,
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  Star,
  ThumbsUp,
  Bookmark,
  RefreshCw,
  Wand2,
  Download,
  Award,
  Clock,
  Cpu
} from 'lucide-react'

interface AIInsight {
  id: string
  type: 'breakthrough' | 'optimization' | 'warning' | 'discovery' | 'recommendation'
  title: string
  description: string
  confidence: number
  impact: 'critical' | 'high' | 'medium' | 'low'
  actionable: boolean
  recommendations: string[]
  evidence: string[]
  tags: string[]
  generatedAt: string
  aiModel: string
  processingTime: number
  liked?: boolean
  bookmarked?: boolean
  priority: number
}

interface ConsistentInsightsGeneratorProps {
  experiment?: any
  onSaveInsight?: (insight: AIInsight) => void
  onShareInsight?: (insight: AIInsight) => void
  onApplyRecommendation?: (recommendation: string) => void
  className?: string
}

// 洞察类型配置
const INSIGHT_TYPES = {
  breakthrough: { label: '突破性发现', color: '#10b981', bg: '#dcfce7' },
  optimization: { label: '优化建议', color: '#3b82f6', bg: '#dbeafe' },
  warning: { label: '警告提醒', color: '#f59e0b', bg: '#fef3c7' },
  discovery: { label: '新发现', color: '#8b5cf6', bg: '#f3e8ff' },
  recommendation: { label: '推荐方案', color: '#ec4899', bg: '#fce7f3' }
}

export default function ConsistentInsightsGenerator({
  experiment,
  onSaveInsight,
  onShareInsight,
  onApplyRecommendation,
  className
}: ConsistentInsightsGeneratorProps) {
  const [insights, setInsights] = useState<AIInsight[]>([])
  const [loading, setLoading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  // 模拟高质量洞察数据
  const mockInsights: AIInsight[] = [
    {
      id: '1',
      type: 'breakthrough',
      title: '🚀 模型性能突破性提升',
      description: '通过优化注意力机制和引入残差连接，模型在测试集上的准确率提升了12.3%，达到了业界领先水平。',
      confidence: 0.96,
      impact: 'critical',
      actionable: true,
      recommendations: [
        '立即将优化后的模型部署到生产环境',
        '准备技术论文发表，分享研究成果',
        '基于此突破开展下一阶段的研究计划'
      ],
      evidence: [
        '测试准确率从75.2%提升至87.5%',
        '推理速度提升35%，延迟降低至120ms',
        '在5个不同数据集上均表现优异'
      ],
      tags: ['突破性进展', '性能优化', '注意力机制', '生产就绪'],
      generatedAt: '2025-01-31T10:30:00Z',
      aiModel: 'GPT-4 Turbo',
      processingTime: 2.3,
      liked: true,
      bookmarked: true,
      priority: 10
    },
    {
      id: '2',
      type: 'optimization',
      title: '⚡ 数据处理流程优化机会',
      description: '发现数据预处理阶段存在冗余操作，通过并行化处理和缓存机制，可以将整体训练时间减少40%。',
      confidence: 0.89,
      impact: 'high',
      actionable: true,
      recommendations: [
        '实施数据预处理并行化',
        '建立智能缓存系统',
        '优化数据加载器配置'
      ],
      evidence: [
        '当前预处理占用总时间的60%',
        '并行化测试显示4倍速度提升',
        '缓存命中率可达85%'
      ],
      tags: ['性能优化', '数据处理', '并行化', '缓存'],
      generatedAt: '2025-01-31T09:45:00Z',
      aiModel: 'Claude-3.5 Sonnet',
      processingTime: 1.8,
      liked: false,
      bookmarked: true,
      priority: 8
    }
  ]

  useEffect(() => {
    setLoading(true)
    setTimeout(() => {
      setInsights(mockInsights)
      setLoading(false)
    }, 1500)
  }, [])

  const generateNewInsights = async () => {
    setGenerating(true)
    setTimeout(() => {
      const newInsight: AIInsight = {
        id: Date.now().toString(),
        type: 'recommendation',
        title: '🎯 超参数调优建议',
        description: '基于贝叶斯优化分析，建议调整学习率和批次大小的组合，预计可提升模型收敛速度30%。',
        confidence: 0.78,
        impact: 'medium',
        actionable: true,
        recommendations: [
          '学习率调整至0.001',
          '批次大小增加至128',
          '使用余弦退火调度器'
        ],
        evidence: [
          '当前配置收敛较慢',
          '贝叶斯优化推荐参数组合',
          '类似实验中效果显著'
        ],
        tags: ['超参数优化', '贝叶斯优化', '收敛速度'],
        generatedAt: new Date().toISOString(),
        aiModel: 'GPT-4 Turbo',
        processingTime: 1.5,
        liked: false,
        bookmarked: false,
        priority: 7
      }
      
      setInsights(prev => [newInsight, ...prev])
      setGenerating(false)
    }, 3000)
  }

  const toggleLike = (id: string) => {
    setInsights(prev => prev.map(insight =>
      insight.id === id ? { ...insight, liked: !insight.liked } : insight
    ))
  }

  const toggleBookmark = (id: string) => {
    setInsights(prev => prev.map(insight =>
      insight.id === id ? { ...insight, bookmarked: !insight.bookmarked } : insight
    ))
  }

  const getImpactBadge = (impact: string) => {
    const styles = {
      critical: { bg: '#fef2f2', color: '#991b1b', border: '#fecaca' },
      high: { bg: '#fff7ed', color: '#9a3412', border: '#fed7aa' },
      medium: { bg: '#fefce8', color: '#a16207', border: '#fde68a' },
      low: { bg: '#f0fdf4', color: '#166534', border: '#bbf7d0' }
    }
    
    const labels = {
      critical: '关键影响',
      high: '高影响',
      medium: '中等影响',
      low: '低影响'
    }
    
    const style = styles[impact as keyof typeof styles]
    
    return (
      <span style={{
        padding: '0.25rem 0.75rem',
        background: style.bg,
        color: style.color,
        border: `1px solid ${style.border}`,
        borderRadius: '1rem',
        fontSize: '0.75rem',
        fontWeight: '500'
      }}>
        {labels[impact as keyof typeof labels]}
      </span>
    )
  }

  const filteredInsights = insights.filter(insight => {
    if (selectedCategory !== 'all' && insight.type !== selectedCategory) return false
    return true
  })

  if (loading) {
    return (
      <div style={{
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1.5rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        padding: '3rem',
        textAlign: 'center'
      }}>
        <div style={{
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          width: '5rem',
          height: '5rem',
          background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
          borderRadius: '1.5rem',
          marginBottom: '1.5rem'
        }}>
          <Brain style={{ height: '2.5rem', width: '2.5rem', color: 'white' }} />
        </div>
        <h3 style={{
          fontSize: '1.5rem',
          fontWeight: 'bold',
          background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          color: 'transparent',
          margin: '0 0 0.75rem 0'
        }}>
          AI正在深度分析实验数据
        </h3>
        <p style={{ color: '#6b7280', margin: '0 0 1.5rem 0', maxWidth: '28rem', marginLeft: 'auto', marginRight: 'auto' }}>
          我们的AI系统正在运用先进的机器学习算法，从多个维度分析您的实验数据，生成有价值的洞察和建议
        </p>
        <div style={{
          width: '20rem',
          height: '0.75rem',
          background: '#f3f4f6',
          borderRadius: '0.375rem',
          margin: '0 auto 1rem',
          overflow: 'hidden'
        }}>
          <div style={{
            width: '75%',
            height: '100%',
            background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
            borderRadius: '0.375rem',
            transition: 'width 0.3s ease'
          }}></div>
        </div>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '1.5rem',
          fontSize: '0.875rem',
          color: '#6b7280'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <div style={{ width: '0.5rem', height: '0.5rem', background: '#3b82f6', borderRadius: '50%' }}></div>
            数据预处理
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <div style={{ width: '0.5rem', height: '0.5rem', background: '#8b5cf6', borderRadius: '50%' }}></div>
            模式识别
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <div style={{ width: '0.5rem', height: '0.5rem', background: '#10b981', borderRadius: '50%' }}></div>
            洞察生成
          </div>
        </div>
      </div>
    )
  }

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }} className={className}>
      {/* 标题区域 */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(20px)',
        borderRadius: '1.5rem',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        padding: '2rem'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '1.5rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <div style={{
              padding: '1rem',
              background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
              borderRadius: '1rem',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
            }}>
              <Brain style={{ height: '2rem', width: '2rem', color: 'white' }} />
            </div>
            <div>
              <h1 style={{
                fontSize: '1.875rem',
                fontWeight: 'bold',
                background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                color: 'transparent',
                margin: 0
              }}>
                AI洞察生成中心
              </h1>
              <p style={{ color: '#6b7280', margin: '0.25rem 0 0 0' }}>智能分析实验数据，生成可执行的深度洞察</p>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <button
              onClick={generateNewInsights}
              disabled={generating}
              style={{
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: generating ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                transition: 'all 0.3s ease',
                opacity: generating ? 0.6 : 1
              }}
              onMouseEnter={(e) => {
                if (!generating) {
                  e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                  e.currentTarget.style.transform = 'translateY(-1px)'
                }
              }}
              onMouseLeave={(e) => {
                if (!generating) {
                  e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                  e.currentTarget.style.transform = 'translateY(0)'
                }
              }}
            >
              {generating ? (
                <>
                  <RefreshCw style={{ 
                    height: '1rem', 
                    width: '1rem',
                    animation: 'spin 1s linear infinite'
                  }} />
                  生成中...
                </>
              ) : (
                <>
                  <Wand2 style={{ height: '1rem', width: '1rem' }} />
                  生成新洞察
                </>
              )}
            </button>
            <button
              onClick={() => console.log('导出报告')}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                padding: '0.5rem 1rem',
                fontSize: '0.875rem',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.borderColor = '#8b5cf6'
                e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.borderColor = '#d1d5db'
                e.currentTarget.style.boxShadow = 'none'
              }}
            >
              <Download style={{ height: '1rem', width: '1rem' }} />
              导出报告
            </button>
          </div>
        </div>

        {/* 筛选控制 */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <span style={{ fontSize: '0.875rem', fontWeight: '500', color: '#374151' }}>类别：</span>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              style={{
                background: 'white',
                border: '1px solid #d1d5db',
                borderRadius: '0.5rem',
                padding: '0.5rem 0.75rem',
                fontSize: '0.875rem',
                cursor: 'pointer',
                transition: 'border-color 0.3s ease'
              }}
              onFocus={(e) => e.currentTarget.style.borderColor = '#3b82f6'}
              onBlur={(e) => e.currentTarget.style.borderColor = '#d1d5db'}
            >
              <option value="all">全部类别</option>
              <option value="breakthrough">突破性发现</option>
              <option value="optimization">优化建议</option>
              <option value="warning">警告提醒</option>
              <option value="discovery">新发现</option>
              <option value="recommendation">推荐方案</option>
            </select>
          </div>
        </div>
      </div>

      {/* 洞察统计概览 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '1.5rem'
      }}>
        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderRadius: '1rem',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          padding: '1.5rem',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(-2px)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(0)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#10b981', margin: '0 0 0.25rem 0' }}>总洞察数</p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#065f46', margin: 0 }}>{insights.length}</p>
            </div>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #10b981, #059669)',
              borderRadius: '0.75rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <Lightbulb style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
            </div>
          </div>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderRadius: '1rem',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          padding: '1.5rem',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(-2px)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(0)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#3b82f6', margin: '0 0 0.25rem 0' }}>可执行建议</p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#1e40af', margin: 0 }}>
                {insights.filter(i => i.actionable).length}
              </p>
            </div>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #3b82f6, #2563eb)',
              borderRadius: '0.75rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <Target style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
            </div>
          </div>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderRadius: '1rem',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          padding: '1.5rem',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(-2px)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(0)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#8b5cf6', margin: '0 0 0.25rem 0' }}>平均置信度</p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#6b21a8', margin: 0 }}>
                {insights.length > 0 ? Math.round(insights.reduce((acc, i) => acc + i.confidence, 0) / insights.length * 100) : 0}%
              </p>
            </div>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #8b5cf6, #7c3aed)',
              borderRadius: '0.75rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <Award style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
            </div>
          </div>
        </div>

        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderRadius: '1rem',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
          padding: '1.5rem',
          transition: 'all 0.3s ease',
          cursor: 'pointer'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(-2px)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
          e.currentTarget.style.transform = 'translateY(0)'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div>
              <p style={{ fontSize: '0.875rem', fontWeight: '500', color: '#f59e0b', margin: '0 0 0.25rem 0' }}>高影响洞察</p>
              <p style={{ fontSize: '2rem', fontWeight: 'bold', color: '#d97706', margin: 0 }}>
                {insights.filter(i => i.impact === 'critical' || i.impact === 'high').length}
              </p>
            </div>
            <div style={{
              padding: '0.75rem',
              background: 'linear-gradient(to right, #f59e0b, #d97706)',
              borderRadius: '0.75rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              <TrendingUp style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
            </div>
          </div>
        </div>
      </div>

      {/* 洞察卡片列表 */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
        {filteredInsights.length === 0 ? (
          <div style={{
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '1.5rem',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            padding: '3rem',
            textAlign: 'center'
          }}>
            <div style={{
              display: 'inline-flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: '4rem',
              height: '4rem',
              background: '#f3f4f6',
              borderRadius: '1rem',
              marginBottom: '1rem'
            }}>
              <Brain style={{ height: '2rem', width: '2rem', color: '#9ca3af' }} />
            </div>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: '#111827', margin: '0 0 0.5rem 0' }}>
              暂无匹配的洞察
            </h3>
            <p style={{ color: '#6b7280', margin: '0 0 1rem 0' }}>尝试调整筛选条件或生成新的洞察</p>
            <button
              onClick={generateNewInsights}
              disabled={generating}
              style={{
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                color: 'white',
                border: 'none',
                borderRadius: '0.5rem',
                padding: '0.75rem 1.5rem',
                fontSize: '0.875rem',
                fontWeight: '600',
                cursor: generating ? 'not-allowed' : 'pointer',
                display: 'inline-flex',
                alignItems: 'center',
                gap: '0.5rem',
                opacity: generating ? 0.6 : 1
              }}
            >
              <Wand2 style={{ height: '1rem', width: '1rem' }} />
              生成洞察
            </button>
          </div>
        ) : (
          filteredInsights.map((insight) => {
            const typeConfig = INSIGHT_TYPES[insight.type]

            return (
              <div
                key={insight.id}
                style={{
                  background: 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: '1.5rem',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  borderLeft: `4px solid ${typeConfig.color}`,
                  boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
                  padding: '2rem',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.boxShadow = '0 25px 50px -12px rgba(0, 0, 0, 0.15)'
                  e.currentTarget.style.transform = 'translateY(-2px)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
                  e.currentTarget.style.transform = 'translateY(0)'
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
                  {/* 洞察头部 */}
                  <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                    <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem', flex: 1 }}>
                      <div style={{
                        padding: '0.75rem',
                        background: `linear-gradient(to right, ${typeConfig.color}, ${typeConfig.color}dd)`,
                        borderRadius: '0.75rem',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                      }}>
                        {insight.type === 'breakthrough' && <CheckCircle2 style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />}
                        {insight.type === 'optimization' && <Zap style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />}
                        {insight.type === 'warning' && <AlertTriangle style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />}
                        {insight.type === 'discovery' && <Star style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />}
                        {insight.type === 'recommendation' && <Target style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />}
                      </div>
                      <div style={{ flex: 1 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem', marginBottom: '0.5rem' }}>
                          <h3 style={{ fontSize: '1.25rem', fontWeight: 'bold', color: '#111827', margin: 0 }}>
                            {insight.title}
                          </h3>
                          {getImpactBadge(insight.impact)}
                        </div>
                        <p style={{ color: '#374151', lineHeight: '1.6', margin: '0 0 1rem 0' }}>
                          {insight.description}
                        </p>

                        {/* 置信度和元数据 */}
                        <div style={{ display: 'flex', alignItems: 'center', gap: '1.5rem', fontSize: '0.875rem', color: '#6b7280' }}>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                            <Award style={{ height: '1rem', width: '1rem' }} />
                            <span>置信度: {Math.round(insight.confidence * 100)}%</span>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                            <Clock style={{ height: '1rem', width: '1rem' }} />
                            <span>处理时间: {insight.processingTime}s</span>
                          </div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                            <Cpu style={{ height: '1rem', width: '1rem' }} />
                            <span>{insight.aiModel}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', marginLeft: '1rem' }}>
                      <button
                        onClick={() => toggleLike(insight.id)}
                        style={{
                          background: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '0.375rem',
                          padding: '0.5rem',
                          cursor: 'pointer',
                          color: insight.liked ? '#ef4444' : '#9ca3af',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = '#f9fafb'
                          e.currentTarget.style.borderColor = '#d1d5db'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'white'
                          e.currentTarget.style.borderColor = '#e5e7eb'
                        }}
                      >
                        <ThumbsUp style={{ height: '1rem', width: '1rem' }} />
                      </button>
                      <button
                        onClick={() => toggleBookmark(insight.id)}
                        style={{
                          background: 'white',
                          border: '1px solid #e5e7eb',
                          borderRadius: '0.375rem',
                          padding: '0.5rem',
                          cursor: 'pointer',
                          color: insight.bookmarked ? '#f59e0b' : '#9ca3af',
                          transition: 'all 0.3s ease'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = '#f9fafb'
                          e.currentTarget.style.borderColor = '#d1d5db'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'white'
                          e.currentTarget.style.borderColor = '#e5e7eb'
                        }}
                      >
                        <Bookmark style={{ height: '1rem', width: '1rem' }} />
                      </button>
                    </div>
                  </div>

                  {/* 置信度进度条 */}
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.875rem' }}>
                      <span style={{ fontWeight: '500', color: '#374151' }}>AI置信度评估</span>
                      <span style={{ color: '#6b7280' }}>{Math.round(insight.confidence * 100)}%</span>
                    </div>
                    <div style={{
                      width: '100%',
                      height: '0.5rem',
                      background: '#f3f4f6',
                      borderRadius: '0.25rem',
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${insight.confidence * 100}%`,
                        height: '100%',
                        background: `linear-gradient(to right, ${typeConfig.color}, ${typeConfig.color}dd)`,
                        borderRadius: '0.25rem',
                        transition: 'width 0.3s ease'
                      }}></div>
                    </div>
                  </div>

                  {/* 标签和元数据 */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    paddingTop: '1rem',
                    borderTop: '1px solid rgba(255, 255, 255, 0.4)'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem', flexWrap: 'wrap' }}>
                      {insight.tags.map((tag, index) => (
                        <span
                          key={index}
                          style={{
                            padding: '0.25rem 0.75rem',
                            background: 'rgba(255, 255, 255, 0.6)',
                            color: '#374151',
                            borderRadius: '1rem',
                            fontSize: '0.75rem',
                            fontWeight: '500',
                            border: '1px solid rgba(255, 255, 255, 0.4)'
                          }}
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', fontSize: '0.75rem', color: '#9ca3af' }}>
                      <span>优先级: {insight.priority}/10</span>
                      <span>{new Date(insight.generatedAt).toLocaleString('zh-CN')}</span>
                    </div>
                  </div>
                </div>
              </div>
            )
          })
        )}
      </div>
    </div>
  )
}
