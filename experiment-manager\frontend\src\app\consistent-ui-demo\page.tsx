/**
 * 与现有风格一致的UI改进展示页面
 * 使用内联样式，保持与项目整体设计的一致性
 */

'use client'

import React, { useState } from 'react'
import { 
  BarChart3, 
  Brain, 
  Users, 
  Activity,
  FlaskConical,
  ArrowRight,
  CheckCircle,
  Star,
  Zap
} from 'lucide-react'

// 导入与现有风格一致的组件
import ConsistentDataAnalytics from '../../components/analytics/consistent-data-analytics'
import ConsistentInsightsGenerator from '../../components/insights/consistent-insights-generator'

// 模拟实验数据
const mockExperiment = {
  id: 'exp-001',
  name: '深度学习模型优化实验',
  description: '通过注意力机制和残差连接优化深度学习模型性能',
  status: 'completed' as const,
  created_at: '2025-01-31T08:00:00Z',
  updated_at: '2025-01-31T12:00:00Z'
}

export default function ConsistentUIDemo() {
  const [activeModule, setActiveModule] = useState<'analytics' | 'insights' | 'collaboration' | 'dashboard'>('analytics')

  const modules = [
    {
      id: 'analytics' as const,
      name: '数据分析',
      description: '改进的数据可视化和分析功能',
      icon: BarChart3,
      status: '已改进',
      improvements: ['更清晰的图表展示', '增强的交互功能', '实时数据刷新', '多种图表类型']
    },
    {
      id: 'insights' as const,
      name: '洞察生成',
      description: '智能洞察生成和展示',
      icon: Brain,
      status: '已改进',
      improvements: ['AI洞察生成', '置信度展示', '可执行建议', '智能分类']
    },
    {
      id: 'collaboration' as const,
      name: '协作讨论',
      description: '团队协作和讨论功能',
      icon: Users,
      status: '计划中',
      improvements: ['实时协作', '评论系统', '团队管理', '通知提醒']
    },
    {
      id: 'dashboard' as const,
      name: '统计仪表板',
      description: '系统监控和统计展示',
      icon: Activity,
      status: '计划中',
      improvements: ['实时监控', '性能指标', '系统状态', '数据统计']
    }
  ]

  const currentModule = modules.find(m => m.id === activeModule)

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fafc 0%, rgba(219, 234, 254, 0.3) 50%, rgba(224, 231, 255, 0.2) 100%)',
      padding: '2rem'
    }}>
      <div style={{
        maxWidth: '1400px',
        margin: '0 auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '2rem'
      }}>
        
        {/* 页面标题 */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderRadius: '1.5rem',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
          padding: '2rem',
          textAlign: 'center'
        }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '5rem',
            height: '5rem',
            background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
            borderRadius: '1.5rem',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
            marginBottom: '1.5rem'
          }}>
            <FlaskConical style={{ height: '2.5rem', width: '2.5rem', color: 'white' }} />
          </div>
          <h1 style={{
            fontSize: 'clamp(2rem, 4vw, 2.5rem)',
            fontWeight: 'bold',
            background: 'linear-gradient(to right, #2563eb, #8b5cf6)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            color: 'transparent',
            margin: '0 0 1rem 0'
          }}>
            UI改进展示中心
          </h1>
          <p style={{
            fontSize: '1.25rem',
            color: '#6b7280',
            maxWidth: '48rem',
            margin: '0 auto 1.5rem',
            lineHeight: '1.6'
          }}>
            基于现有设计风格的渐进式UI改进，保持一致性的同时提升用户体验
          </p>
          
          {/* 改进统计 */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
            gap: '1rem',
            marginTop: '2rem'
          }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>2</div>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>已改进模块</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#8b5cf6' }}>2</div>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>计划改进</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>100%</div>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>风格一致性</div>
            </div>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>0</div>
              <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>破坏性变更</div>
            </div>
          </div>
        </div>

        {/* 模块选择器 */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
          gap: '1.5rem'
        }}>
          {modules.map((module) => {
            const IconComponent = module.icon
            const isActive = activeModule === module.id
            const isImproved = module.status === '已改进'
            
            return (
              <div 
                key={module.id}
                style={{
                  background: isActive 
                    ? 'rgba(59, 130, 246, 0.1)' 
                    : 'rgba(255, 255, 255, 0.9)',
                  backdropFilter: 'blur(20px)',
                  borderRadius: '1rem',
                  border: isActive 
                    ? '2px solid #3b82f6' 
                    : '1px solid rgba(255, 255, 255, 0.2)',
                  boxShadow: isActive 
                    ? '0 20px 25px -5px rgba(59, 130, 246, 0.1)' 
                    : '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
                  padding: '1.5rem',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease'
                }}
                onClick={() => setActiveModule(module.id)}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1)'
                    e.currentTarget.style.transform = 'translateY(-2px)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <div style={{
                      padding: '0.75rem',
                      background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                      borderRadius: '0.75rem',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
                    }}>
                      <IconComponent style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
                    </div>
                    <div style={{
                      padding: '0.25rem 0.75rem',
                      background: isImproved ? '#dcfce7' : '#fef3c7',
                      color: isImproved ? '#166534' : '#92400e',
                      borderRadius: '1rem',
                      fontSize: '0.75rem',
                      fontWeight: '500'
                    }}>
                      {module.status}
                    </div>
                  </div>
                  
                  <div>
                    <h3 style={{
                      fontSize: '1.125rem',
                      fontWeight: 'bold',
                      color: '#111827',
                      margin: '0 0 0.5rem 0'
                    }}>
                      {module.name}
                    </h3>
                    <p style={{
                      fontSize: '0.875rem',
                      color: '#6b7280',
                      margin: '0 0 1rem 0'
                    }}>
                      {module.description}
                    </p>
                    
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
                      {module.improvements.map((improvement, index) => (
                        <div key={index} style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                          <Star style={{ height: '0.75rem', width: '0.75rem', color: '#f59e0b' }} />
                          <span style={{ fontSize: '0.75rem', color: '#6b7280' }}>{improvement}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <button style={{
                    width: '100%',
                    padding: '0.75rem',
                    background: isActive 
                      ? 'linear-gradient(to right, #3b82f6, #8b5cf6)' 
                      : 'white',
                    color: isActive ? 'white' : '#374151',
                    border: isActive ? 'none' : '1px solid #d1d5db',
                    borderRadius: '0.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '0.5rem',
                    transition: 'all 0.3s ease'
                  }}>
                    {isActive ? '正在展示' : isImproved ? '查看改进' : '即将改进'}
                    <ArrowRight style={{ height: '1rem', width: '1rem' }} />
                  </button>
                </div>
              </div>
            )
          })}
        </div>

        {/* 当前模块信息 */}
        {currentModule && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.8)',
            backdropFilter: 'blur(20px)',
            borderRadius: '1rem',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
            padding: '1.5rem'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
              <div style={{
                padding: '0.75rem',
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                borderRadius: '0.75rem',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}>
                <currentModule.icon style={{ height: '1.5rem', width: '1.5rem', color: 'white' }} />
              </div>
              <div>
                <h2 style={{
                  fontSize: '1.5rem',
                  fontWeight: 'bold',
                  color: '#111827',
                  margin: 0
                }}>
                  {currentModule.name}模块
                </h2>
                <p style={{ color: '#6b7280', margin: '0.25rem 0 0 0' }}>{currentModule.description}</p>
              </div>
              <div style={{ marginLeft: 'auto' }}>
                <div style={{
                  padding: '0.5rem 1rem',
                  background: currentModule.status === '已改进' ? '#dcfce7' : '#fef3c7',
                  color: currentModule.status === '已改进' ? '#166534' : '#92400e',
                  borderRadius: '1rem',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  {currentModule.status === '已改进' ? (
                    <CheckCircle style={{ height: '1rem', width: '1rem' }} />
                  ) : (
                    <Zap style={{ height: '1rem', width: '1rem' }} />
                  )}
                  {currentModule.status}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 模块内容展示 */}
        <div>
          {activeModule === 'analytics' && (
            <ConsistentDataAnalytics
              experiment={mockExperiment}
              onExportData={(format) => console.log('导出数据:', format)}
              onRefreshData={() => console.log('刷新数据')}
            />
          )}

          {activeModule === 'insights' && (
            <ConsistentInsightsGenerator
              experiment={mockExperiment}
              onSaveInsight={(insight) => console.log('保存洞察:', insight)}
              onShareInsight={(insight) => console.log('分享洞察:', insight)}
              onApplyRecommendation={(rec) => console.log('应用建议:', rec)}
            />
          )}

          {(activeModule !== 'analytics' && activeModule !== 'insights') && (
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '1.5rem',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
              padding: '3rem',
              textAlign: 'center'
            }}>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '4rem',
                height: '4rem',
                background: 'linear-gradient(to right, #3b82f6, #8b5cf6)',
                borderRadius: '1rem',
                marginBottom: '1.5rem'
              }}>
                <currentModule.icon style={{ height: '2rem', width: '2rem', color: 'white' }} />
              </div>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                color: '#111827',
                margin: '0 0 1rem 0'
              }}>
                {currentModule.name}模块
              </h3>
              <p style={{
                color: '#6b7280',
                margin: '0 0 1.5rem 0',
                maxWidth: '32rem',
                marginLeft: 'auto',
                marginRight: 'auto'
              }}>
                此模块正在开发中，将采用与现有设计风格一致的改进方案，确保整体用户体验的连贯性。
              </p>
              <div style={{
                padding: '1rem',
                background: 'rgba(59, 130, 246, 0.1)',
                borderRadius: '0.75rem',
                border: '1px solid rgba(59, 130, 246, 0.2)'
              }}>
                <p style={{
                  fontSize: '0.875rem',
                  color: '#1d4ed8',
                  margin: 0,
                  fontWeight: '500'
                }}>
                  💡 即将推出：保持现有风格的渐进式改进
                </p>
              </div>
            </div>
          )}
        </div>

        {/* 底部信息 */}
        <div style={{ textAlign: 'center', padding: '2rem 0' }}>
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.5rem',
            padding: '1rem 1.5rem',
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '2rem',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
          }}>
            <CheckCircle style={{ height: '1.25rem', width: '1.25rem', color: '#10b981' }} />
            <span style={{
              fontSize: '0.875rem',
              fontWeight: '500',
              color: '#374151'
            }}>
              渐进式改进 • 保持风格一致性 • 零破坏性变更
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
