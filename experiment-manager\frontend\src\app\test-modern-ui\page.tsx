/**
 * 简单的现代化UI测试页面
 * 用于验证组件是否正常工作
 */

'use client'

import React, { useState } from 'react'
import { 
  BarChart3, 
  Brain, 
  Users, 
  Activity,
  Sparkles,
  CheckCircle2,
  AlertTriangle,
  Info
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { Badge } from '../../components/ui/badge'

export default function TestModernUI() {
  const [testStatus, setTestStatus] = useState('ready')
  const [componentTests, setComponentTests] = useState({
    analytics: 'pending',
    insights: 'pending', 
    collaboration: 'pending',
    dashboard: 'pending'
  })

  const testComponent = async (componentName: string) => {
    setComponentTests(prev => ({ ...prev, [componentName]: 'testing' }))
    
    try {
      // 模拟组件加载测试
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 尝试动态导入组件
      let component
      switch (componentName) {
        case 'analytics':
          component = await import('../../components/analytics/modern-data-analytics')
          break
        case 'insights':
          component = await import('../../components/insights/modern-insights-generator')
          break
        case 'collaboration':
          component = await import('../../components/collaboration/modern-collaboration-hub')
          break
        case 'dashboard':
          component = await import('../../components/dashboard/modern-statistics-dashboard')
          break
      }
      
      if (component) {
        setComponentTests(prev => ({ ...prev, [componentName]: 'success' }))
      } else {
        setComponentTests(prev => ({ ...prev, [componentName]: 'error' }))
      }
    } catch (error) {
      console.error(`测试 ${componentName} 组件失败:`, error)
      setComponentTests(prev => ({ ...prev, [componentName]: 'error' }))
    }
  }

  const testAllComponents = async () => {
    setTestStatus('testing')
    await Promise.all([
      testComponent('analytics'),
      testComponent('insights'),
      testComponent('collaboration'),
      testComponent('dashboard')
    ])
    setTestStatus('completed')
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle2 className="h-5 w-5 text-green-600" />
      case 'error': return <AlertTriangle className="h-5 w-5 text-red-600" />
      case 'testing': return <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
      default: return <Info className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800 border-green-200'
      case 'error': return 'bg-red-100 text-red-800 border-red-200'
      case 'testing': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-purple-50 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        
        {/* 页面标题 */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mb-4 shadow-lg">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-2">
            🧪 现代化UI组件测试
          </h1>
          <p className="text-gray-600">
            验证所有现代化组件是否正常工作
          </p>
        </div>

        {/* 测试控制面板 */}
        <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-6 w-6 text-blue-600" />
              组件测试控制面板
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-gray-700">测试状态:</span>
              <Badge className={getStatusColor(testStatus)}>
                {testStatus === 'ready' && '准备就绪'}
                {testStatus === 'testing' && '测试中...'}
                {testStatus === 'completed' && '测试完成'}
              </Badge>
            </div>
            
            <Button 
              onClick={testAllComponents}
              disabled={testStatus === 'testing'}
              className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
            >
              {testStatus === 'testing' ? '测试进行中...' : '开始测试所有组件'}
            </Button>
          </CardContent>
        </Card>

        {/* 组件测试结果 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          
          {/* 数据分析组件 */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl hover:shadow-2xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-violet-500 to-purple-600 rounded-lg">
                    <BarChart3 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">数据分析模块</h3>
                    <p className="text-sm text-gray-600">modern-data-analytics.tsx</p>
                  </div>
                </div>
                {getStatusIcon(componentTests.analytics)}
              </div>
              
              <div className="space-y-2">
                <Badge className={getStatusColor(componentTests.analytics)}>
                  {componentTests.analytics === 'pending' && '等待测试'}
                  {componentTests.analytics === 'testing' && '测试中'}
                  {componentTests.analytics === 'success' && '✅ 组件正常'}
                  {componentTests.analytics === 'error' && '❌ 加载失败'}
                </Badge>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => testComponent('analytics')}
                  disabled={componentTests.analytics === 'testing'}
                  className="w-full"
                >
                  单独测试
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 洞察生成组件 */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl hover:shadow-2xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Brain className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">洞察生成模块</h3>
                    <p className="text-sm text-gray-600">modern-insights-generator.tsx</p>
                  </div>
                </div>
                {getStatusIcon(componentTests.insights)}
              </div>
              
              <div className="space-y-2">
                <Badge className={getStatusColor(componentTests.insights)}>
                  {componentTests.insights === 'pending' && '等待测试'}
                  {componentTests.insights === 'testing' && '测试中'}
                  {componentTests.insights === 'success' && '✅ 组件正常'}
                  {componentTests.insights === 'error' && '❌ 加载失败'}
                </Badge>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => testComponent('insights')}
                  disabled={componentTests.insights === 'testing'}
                  className="w-full"
                >
                  单独测试
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 协作讨论组件 */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl hover:shadow-2xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">协作讨论模块</h3>
                    <p className="text-sm text-gray-600">modern-collaboration-hub.tsx</p>
                  </div>
                </div>
                {getStatusIcon(componentTests.collaboration)}
              </div>
              
              <div className="space-y-2">
                <Badge className={getStatusColor(componentTests.collaboration)}>
                  {componentTests.collaboration === 'pending' && '等待测试'}
                  {componentTests.collaboration === 'testing' && '测试中'}
                  {componentTests.collaboration === 'success' && '✅ 组件正常'}
                  {componentTests.collaboration === 'error' && '❌ 加载失败'}
                </Badge>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => testComponent('collaboration')}
                  disabled={componentTests.collaboration === 'testing'}
                  className="w-full"
                >
                  单独测试
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 统计仪表板组件 */}
          <Card className="border-0 shadow-xl bg-white/80 backdrop-blur-xl hover:shadow-2xl transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-gradient-to-br from-amber-500 to-orange-600 rounded-lg">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">统计仪表板模块</h3>
                    <p className="text-sm text-gray-600">modern-statistics-dashboard.tsx</p>
                  </div>
                </div>
                {getStatusIcon(componentTests.dashboard)}
              </div>
              
              <div className="space-y-2">
                <Badge className={getStatusColor(componentTests.dashboard)}>
                  {componentTests.dashboard === 'pending' && '等待测试'}
                  {componentTests.dashboard === 'testing' && '测试中'}
                  {componentTests.dashboard === 'success' && '✅ 组件正常'}
                  {componentTests.dashboard === 'error' && '❌ 加载失败'}
                </Badge>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => testComponent('dashboard')}
                  disabled={componentTests.dashboard === 'testing'}
                  className="w-full"
                >
                  单独测试
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 说明信息 */}
        <Card className="border-0 shadow-xl bg-blue-50/80 backdrop-blur-xl">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">测试说明</h3>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 此页面用于验证所有现代化组件是否能正常加载</li>
                  <li>• 如果组件测试失败，请检查浏览器控制台的错误信息</li>
                  <li>• 所有组件测试成功后，可以访问完整的展示页面</li>
                  <li>• 测试页面地址: <code className="bg-blue-100 px-1 rounded">/test-modern-ui</code></li>
                  <li>• 展示页面地址: <code className="bg-blue-100 px-1 rounded">/modern-ui-showcase</code></li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
