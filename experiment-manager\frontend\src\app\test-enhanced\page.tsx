'use client'

import React, { useState } from 'react'
import EnhancedAnalysis from '../../components/retrospective/enhanced-analysis'
import EnhancedInsights from '../../components/retrospective/enhanced-insights'
import EnhancedCollaboration from '../../components/retrospective/enhanced-collaboration'
import EnhancedDashboard from '../../components/retrospective/enhanced-dashboard'
import { Button } from '../../components/ui/button'
import { ExperimentStatus } from '../../types/experiment'

// 模拟实验数据
const mockExperiment = {
  id: '1',
  name: '测试实验',
  description: '这是一个测试实验',
  hypothesis: '测试假设',
  status: ExperimentStatus.COMPLETED,
  created_at: new Date().toISOString(),
  phases: []
}

export default function TestEnhancedPage() {
  const [activeTab, setActiveTab] = useState<'analysis' | 'insights' | 'collaboration' | 'dashboard'>('analysis')

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">增强组件测试页面</h1>
          <div className="flex gap-4">
            <Button 
              onClick={() => setActiveTab('analysis')}
              variant={activeTab === 'analysis' ? 'primary' : 'outline'}
            >
              数据分析
            </Button>
            <Button 
              onClick={() => setActiveTab('insights')}
              variant={activeTab === 'insights' ? 'primary' : 'outline'}
            >
              洞察生成
            </Button>
            <Button 
              onClick={() => setActiveTab('collaboration')}
              variant={activeTab === 'collaboration' ? 'primary' : 'outline'}
            >
              协作讨论
            </Button>
            <Button 
              onClick={() => setActiveTab('dashboard')}
              variant={activeTab === 'dashboard' ? 'primary' : 'outline'}
            >
              统计仪表板
            </Button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          {activeTab === 'analysis' && (
            <EnhancedAnalysis
              experiment={mockExperiment}
              onExport={(format) => console.log('导出:', format)}
            />
          )}
          
          {activeTab === 'insights' && (
            <EnhancedInsights
              experiment={mockExperiment}
              onSaveInsight={(insight) => console.log('保存洞察:', insight)}
              onShareInsight={(insight) => console.log('分享洞察:', insight)}
            />
          )}
          
          {activeTab === 'collaboration' && (
            <EnhancedCollaboration
              experiment={mockExperiment}
              onAddComment={(comment) => console.log('添加评论:', comment)}
              onInviteUser={(userId) => console.log('邀请用户:', userId)}
            />
          )}
          
          {activeTab === 'dashboard' && (
            <EnhancedDashboard
              experiment={mockExperiment}
              onExportReport={(format) => console.log('导出报告:', format)}
            />
          )}
        </div>
      </div>
    </div>
  )
}
